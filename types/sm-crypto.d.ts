declare module 'sm-crypto' {
  interface SM2 {
    /**
     * SM2加密
     * @param msgString 待加密的字符串
     * @param publicKey 公钥
     * @param cipherMode 密文排列方式，1表示C1C3C2，0表示C1C2C3，默认为1
     * @returns 加密后的密文
     */
    doEncrypt(msgString: string, publicKey: string, cipherMode?: number): string;

    /**
     * SM2解密
     * @param encryptData 待解密的密文
     * @param privateKey 私钥
     * @param cipherMode 密文排列方式，1表示C1C3C2，0表示C1C2C3，默认为1
     * @returns 解密后的明文
     */
    doDecrypt(encryptData: string, privateKey: string, cipherMode?: number): string;

    /**
     * SM2签名
     * @param msgString 待签名的字符串
     * @param privateKey 私钥
     * @param options 签名选项
     * @returns 签名结果
     */
    doSignature(msgString: string, privateKey: string, options?: {
      hash?: boolean;
      der?: boolean;
      pointPool?: any;
      publicKey?: string;
    }): string;

    /**
     * SM2验签
     * @param msgString 原始字符串
     * @param signHex 签名
     * @param publicKey 公钥
     * @param options 验签选项
     * @returns 验签结果
     */
    doVerifySignature(msgString: string, signHex: string, publicKey: string, options?: {
      hash?: boolean;
      der?: boolean;
      pointPool?: any;
    }): boolean;

    /**
     * 生成SM2密钥对
     * @returns 包含公钥和私钥的对象
     */
    generateKeyPairHex(): {
      privateKey: string;
      publicKey: string;
    };
  }

  interface SM4 {
    /**
     * SM4加密
     * @param inData 待加密的数据
     * @param key 密钥（32位十六进制字符串）
     * @param options 加密选项
     * @returns 加密后的密文
     */
    encrypt(inData: string, key: string, options?: any): string;

    /**
     * SM4解密
     * @param inData 待解密的密文
     * @param key 密钥（32位十六进制字符串）
     * @param options 解密选项
     * @returns 解密后的明文
     */
    decrypt(inData: string, key: string, options?: any): string;
  }

  interface SM3 {
    /**
     * SM3哈希
     * @param str 待哈希的字符串
     * @returns 哈希值
     */
    sm3(str: string): string;
  }

  const sm2: SM2;
  const sm4: SM4;
  const sm3: SM3;

  export = {
    sm2: SM2,
    sm4: SM4,
    sm3: SM3,
  };
}
