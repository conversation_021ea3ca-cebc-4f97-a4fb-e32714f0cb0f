version: '3.8'

services:
  nextchat:
    build:
      context: .
      dockerfile: Dockerfile.custom
      args:
        DEEPSEEK_API_KEY: ${DEEPSEEK_API_KEY}
        STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
        NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    container_name: nextchat-deepseek