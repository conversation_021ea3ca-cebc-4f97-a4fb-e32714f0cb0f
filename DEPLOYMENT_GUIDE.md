# NextChat 部署指南

## 🎉 构建成功！

您的 NextChat 应用已成功构建，解决了之前的内存不足问题（exit code 137）。

## 📦 构建产物

- **优化镜像**: `nextchat-optimized.tar.gz` (64MB)
- **部署脚本**: `dist/deploy.sh`
- **应用文件**: `dist/standalone/` 目录
- **总大小**: 921MB

## 🔧 解决的问题

1. **内存不足问题**: 使用本地构建 + 轻量级 Docker 镜像策略
2. **Node.js 版本**: 自动切换到 Node.js 18.20.7
3. **代理支持**: 集成了 proxychains 支持代理配置

## 🚀 部署步骤

### 方法一：使用 Docker 镜像（推荐）

1. **上传镜像到服务器**
   ```bash
   scp dist/nextchat-optimized.tar.gz username@server_ip:/path/to/
   ```

2. **在服务器上加载镜像**
   ```bash
   ssh username@server_ip
   cd /path/to/
   gunzip nextchat-optimized.tar.gz
   docker load -i nextchat-optimized.tar
   ```

3. **运行容器**
   ```bash
   # 基本运行
   docker run -d -p 3000:3000 \
     -e DEEPSEEK_API_KEY="your-api-key" \
     --name nextchat \
     nextchat-optimized:latest
   
   # 带代理运行
   docker run -d -p 3000:3000 \
     -e DEEPSEEK_API_KEY="your-api-key" \
     -e PROXY_URL="http://your-proxy:port" \
     --name nextchat \
     nextchat-optimized:latest
   ```

### 方法二：使用部署脚本

1. **上传整个 dist 目录**
   ```bash
   scp -r dist/ username@server_ip:/path/to/
   ```

2. **在服务器上运行部署脚本**
   ```bash
   ssh username@server_ip
   cd /path/to/dist
   ./deploy.sh
   ```

## 🔧 环境变量配置

### 必需变量
- `DEEPSEEK_API_KEY`: 您的 DeepSeek API 密钥

### 可选变量
- `PROXY_URL`: 代理地址（如：`http://localhost:7890`）
- `HTTP_PROXY`: HTTP 代理
- `HTTPS_PROXY`: HTTPS 代理
- `PORT`: 服务端口（默认 3000）

## 🌐 代理配置说明

根据您的部署环境选择正确的代理地址：

- **本地开发**: `http://localhost:7890`
- **Docker 容器**: `http://host.docker.internal:7890`
- **服务器部署**: 根据实际网络配置调整

## 📋 验证部署

1. **检查容器状态**
   ```bash
   docker ps | grep nextchat
   ```

2. **查看日志**
   ```bash
   docker logs nextchat
   ```

3. **访问应用**
   ```
   http://your-server-ip:3000
   ```

## 🛠️ 故障排除

### 容器无法启动
- 检查 API 密钥是否正确设置
- 确认端口 3000 未被占用
- 查看容器日志获取详细错误信息

### 网络连接问题
- 验证代理配置是否正确
- 检查防火墙设置
- 确认 API 服务可达性

### 内存问题
- 本构建已优化内存使用
- 如仍有问题，可增加服务器内存或调整 Docker 内存限制

## 📞 技术支持

如遇到问题，请检查：
1. Docker 版本兼容性
2. 网络连接状态
3. 环境变量配置
4. 服务器资源使用情况

---

**构建时间**: $(date)
**构建版本**: 内存优化版本
**支持平台**: linux/amd64