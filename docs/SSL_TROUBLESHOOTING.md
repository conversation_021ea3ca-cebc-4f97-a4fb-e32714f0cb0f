# SSL证书问题排查指南

## 问题描述

当您在生产环境中遇到以下错误时：

```
details: "fetch failed"
error: "Custom proxy request failed"
```

这通常是由SSL证书验证失败引起的，特别是当请求地址使用HTTPS协议时。

## 常见SSL错误类型

1. **自签名证书错误**
   - `DEPTH_ZERO_SELF_SIGNED_CERT`
   - `self signed certificate`

2. **证书链验证失败**
   - `UNABLE_TO_VERIFY_LEAF_SIGNATURE`
   - `SELF_SIGNED_CERT_IN_CHAIN`

3. **证书过期或无效**
   - `certificate has expired`
   - `certificate is not yet valid`

## 解决方案

### 方案1：环境变量配置（推荐用于开发环境）

在您的环境配置文件（`.env.local` 或 `.env`）中添加：

```bash
# 禁用Node.js的SSL证书验证（仅用于开发环境）
NODE_TLS_REJECT_UNAUTHORIZED=0

# 自定义API配置 - 请替换为您的实际后端API地址
CUSTOM_API_BASE_URL=https://your-backend-api.com
NEXT_CUSTOM_API_BASE_URL=/api/proxy/custom
```

### 方案2：使用HTTP代理

如果您有可用的HTTP代理服务器：

```bash
# 代理配置
PROXY_URL=http://your-proxy-server:port
HTTP_PROXY=http://your-proxy-server:port
HTTPS_PROXY=http://your-proxy-server:port

# 自定义API配置 - 请替换为您的实际后端API地址
CUSTOM_API_BASE_URL=https://your-backend-api.com
```

### 方案3：使用HTTP协议（如果服务器支持）

如果目标服务器同时支持HTTP和HTTPS，可以尝试使用HTTP：

```bash
# 使用HTTP协议避免SSL问题 - 请替换为您的实际后端API地址
CUSTOM_API_BASE_URL=http://your-backend-api.com
```

### 方案4：Docker部署配置

如果您使用Docker部署，在构建时传递环境变量：

```bash
# 构建时传递SSL配置 - 请替换为您的实际后端API地址
docker build \
  --build-arg NODE_TLS_REJECT_UNAUTHORIZED=0 \
  --build-arg CUSTOM_API_BASE_URL=https://your-backend-api.com \
  -t nextchat .

# 运行时传递环境变量 - 请替换为您的实际后端API地址
docker run -d \
  -p 3000:3000 \
  -e NODE_TLS_REJECT_UNAUTHORIZED=0 \
  -e CUSTOM_API_BASE_URL=https://your-backend-api.com \
  nextchat
```

## 生产环境最佳实践

### 1. 获取有效的SSL证书

最安全的解决方案是确保目标服务器使用有效的SSL证书：

- 使用Let's Encrypt等免费证书颁发机构
- 购买商业SSL证书
- 确保证书链完整

### 2. 配置反向代理

使用Nginx等反向代理服务器处理SSL终止：

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location /api/ {
        proxy_pass https://your-backend-api.com;
        proxy_ssl_verify off;  # 仅在必要时使用
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 3. 使用CDN服务

通过Cloudflare等CDN服务来处理SSL证书：

1. 将域名解析指向Cloudflare
2. 启用Cloudflare的SSL/TLS加密
3. 配置源服务器证书

## 调试步骤

### 1. 检查SSL证书状态

```bash
# 使用openssl检查证书 - 请替换为您的实际域名
openssl s_client -connect your-backend-api.com:443 -servername your-backend-api.com

# 使用curl测试连接 - 请替换为您的实际API地址
curl -v https://your-backend-api.com/api/your-endpoint
```

### 2. 查看应用日志

检查NextChat应用的控制台输出，查找SSL相关的错误信息：

```bash
# Docker环境
docker logs your-container-name

# 本地开发
npm run dev
```

### 3. 测试网络连接

```bash
# 测试域名解析 - 请替换为您的实际域名
nslookup your-backend-api.com

# 测试端口连通性 - 请替换为您的实际域名
telnet your-backend-api.com 443
```

## 安全注意事项

⚠️ **重要提醒**：

1. `NODE_TLS_REJECT_UNAUTHORIZED=0` 会禁用所有SSL证书验证，存在安全风险
2. 仅在开发环境或确认安全的情况下使用
3. 生产环境建议使用有效的SSL证书或反向代理
4. 定期更新和检查SSL证书的有效性

## 联系支持

如果以上解决方案都无法解决问题，请：

1. 收集完整的错误日志
2. 记录您的环境配置
3. 提供目标API服务器的SSL证书信息
4. 在GitHub Issues中提交详细的问题报告

## 相关文件

- `app/utils/ssl-config.ts` - SSL配置工具
- `app/utils/fetch.ts` - 网络请求封装
- `app/api/proxy/custom/[...path]/route.ts` - 自定义代理路由
- `.env.template` - 环境变量模板