# API 集成指南

本指南说明如何在 NextChat 中集成自定义认证 API，以及如何组织和配置 API 的最佳实践。

## 项目结构

```
app/
├── components/
│   ├── login.tsx          # 登录页面组件
│   ├── login.module.scss  # 登录页面样式
│   ├── register.tsx       # 注册页面组件
│   ├── register.module.scss # 注册页面样式
│   └── home.tsx           # 主页面路由配置
├── utils/
│   └── auth-api.ts        # 认证 API 工具函数
└── constant.ts            # 路径常量定义
```

## API 组织最佳实践

### 1. 接口设计规范

#### 认证相关接口

```typescript
// 登录接口
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}

// 响应
{
  "success": true,
  "message": "登录成功",
  "token": "jwt_token_here",
  "user": {
    "id": "user_id",
    "name": "用户名",
    "email": "<EMAIL>",
    "avatar": "avatar_url"
  }
}
```

```typescript
// 注册接口
POST /auth/register
Content-Type: application/json

{
  "name": "用户名",
  "email": "<EMAIL>",
  "password": "password123"
}

// 响应格式同登录接口
```

#### 其他认证接口

```typescript
// 获取用户信息
GET /auth/profile
Authorization: Bearer {token}

// 刷新令牌
POST /auth/refresh
Authorization: Bearer {refresh_token}

// 修改密码
PUT /auth/password
Authorization: Bearer {token}
Content-Type: application/json

{
  "oldPassword": "old_password",
  "newPassword": "new_password"
}

// 登出
POST /auth/logout
Authorization: Bearer {token}
```

### 2. 错误处理规范

```typescript
// 错误响应格式
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "details": {} // 可选的详细错误信息
}

// 常见错误码
- AUTH_001: 用户名或密码错误
- AUTH_002: 用户不存在
- AUTH_003: 邮箱已被注册
- AUTH_004: 令牌无效或已过期
- AUTH_005: 权限不足
```

### 3. 安全最佳实践

- 使用 HTTPS 传输
- 密码使用强哈希算法（如 bcrypt）
- JWT 令牌设置合理的过期时间
- 实现刷新令牌机制
- 对敏感操作进行二次验证
- 实现请求频率限制

## 环境配置

### 开发环境

1. 复制 `.env.local.example` 为 `.env.local`
2. 配置你的后端 API 地址：

```bash
# 后端 API 地址（Next.js 代理配置中使用）
CUSTOM_API_BASE_URL=http://localhost:8080

# 前端 API 基础路径（推荐使用代理）
NEXT_CUSTOM_API_BASE_URL=/api
```

### 生产环境

```bash
# 生产环境后端 API 地址
CUSTOM_API_BASE_URL=https://your-api-domain.com

# 前端 API 基础路径
NEXT_CUSTOM_API_BASE_URL=/api
```

## 跨域处理

### 方案一：使用 Next.js 代理（推荐）

项目已配置 Next.js 代理，将 `/api/auth/*` 请求代理到你的后端服务：

```javascript
// next.config.mjs
{
  source: "/api/auth/:path*",
  destination: `${process.env.CUSTOM_API_BASE_URL}/auth/:path*`,
}
```

**优势：**
- 避免跨域问题
- 统一的 API 路径
- 便于开发和调试

### 方案二：直接调用（需要后端配置 CORS）

如果你的后端已经配置了 CORS，可以直接调用：

```bash
# 设置为你的后端地址
NEXT_CUSTOM_API_BASE_URL=http://localhost:8080
```

**注意：** 后端需要配置 CORS 允许前端域名访问。

## API 架构说明

### 统一API客户端

项目采用统一的API客户端架构，提供以下特性：

- **统一错误处理**: 自动处理网络错误、服务器错误和业务错误
- **Token管理**: 自动添加认证头，处理token过期和刷新
- **请求重试**: Token过期时自动刷新并重试请求
- **类型安全**: 完整的TypeScript类型定义
- **模块化设计**: 按功能模块划分API接口

### 文件结构

```
app/
├── api/
│   ├── custom-api/          # 自定义服务API（区别于AI相关API）
│   │   ├── index.ts         # API统一入口
│   │   └── user-api.ts      # 用户相关API
│   ├── openai.ts           # AI相关API
│   ├── anthropic.ts        # AI相关API
│   └── ...
└── utils/
    ├── api-client.ts        # 统一HTTP客户端
    └── auth-api.ts          # 认证相关API
```

### 模块划分

- **auth模块**: 处理登录、注册、认证相关功能
- **user模块**: 处理用户信息、设置、统计等功能
- **api-client**: 统一的HTTP客户端，提供基础请求能力
- **custom-api**: 自定义服务API的统一入口，区别于AI相关的API

### 错误处理

```typescript
try {
  const result = await API.auth.login(data);
  // 处理成功响应
} catch (error) {
  if (error.code === 'TOKEN_EXPIRED') {
    // Token过期，已自动刷新并重试
  } else if (error.code === 'VALIDATION_ERROR') {
    // 参数验证错误
  } else {
    // 其他错误
  }
}
```

## 使用说明

### 1. 启动开发服务器

```bash
# 启动 NextChat
npm run dev

# 确保你的后端服务运行在配置的端口（默认 8080）
```

### 2. 访问认证页面

- 登录页面：`http://localhost:3000/#/login`
- 注册页面：`http://localhost:3000/#/register`

### 3. API 工具函数使用

#### 新版本统一API（推荐）

```typescript
import { API } from '@/app/api/custom-api';

// 登录
try {
  const result = await API.auth.login({ email, password });
  if (result.success) {
    // 登录成功，token和用户信息已自动保存
    console.log('登录成功');
  }
} catch (error) {
  console.error('登录失败:', error.message);
}

// 注册
try {
  const result = await API.auth.register({ name, email, password });
  if (result.success) {
    // 注册成功，token和用户信息已自动保存
    console.log('注册成功');
  }
} catch (error) {
  console.error('注册失败:', error.message);
}

// 获取用户信息
const user = await API.auth.getProfile();

// 更新用户信息
const updatedUser = await API.auth.updateProfile({ name: '新名称' });

// 修改密码
await API.auth.changePassword({ 
  oldPassword: '旧密码', 
  newPassword: '新密码' 
});

// 登出
await API.auth.logout();

// 用户相关API
const settings = await API.user.getSettings();
const stats = await API.user.getStats();
```

#### 兼容性API（仍可使用）

```typescript
import { 
  loginApi, 
  registerApi, 
  getAuthToken, 
  setAuthToken, 
  getUserInfo,
  setUserInfo,
  clearAuth 
} from '@/app/api/custom-api';

// 登录
const result = await loginApi({ email, password });

// 注册
const result = await registerApi({ name, email, password });

// 获取当前令牌
const token = getAuthToken();

// 清除认证信息
clearAuth();
```

## 后端实现建议

### 1. 技术栈推荐

- **Node.js**: Express.js + TypeScript
- **Python**: FastAPI 或 Django REST Framework
- **Java**: Spring Boot
- **Go**: Gin 或 Echo

### 2. 数据库设计

```sql
-- 用户表
CREATE TABLE users (
  id VARCHAR(36) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  avatar VARCHAR(500),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 刷新令牌表
CREATE TABLE refresh_tokens (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  token VARCHAR(500) NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### 3. 中间件配置

```javascript
// Express.js CORS 配置示例
const cors = require('cors');

app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
```

## 故障排除

### 常见问题

1. **跨域错误**
   - 检查 `CUSTOM_API_BASE_URL` 配置
   - 确认后端 CORS 设置
   - 使用代理方案（推荐）

2. **认证失败**
   - 检查 API 接口格式
   - 确认后端服务运行状态
   - 查看浏览器网络请求

3. **样式问题**
   - 确认 SCSS 模块正确导入
   - 检查移动端适配

### 调试技巧

1. 打开浏览器开发者工具查看网络请求
2. 检查控制台错误信息
3. 使用 `console.log` 调试 API 调用
4. 验证环境变量配置

## 扩展功能

### 1. 社交登录

可以扩展支持 Google、GitHub 等第三方登录：

```typescript
// 添加到 auth-api.ts
export const googleLoginApi = async (token: string): Promise<AuthResponse> => {
  // 实现 Google 登录逻辑
};
```

### 2. 多因素认证

```typescript
// 发送验证码
export const sendVerificationCode = async (email: string): Promise<void> => {
  // 实现发送验证码逻辑
};

// 验证码登录
export const verifyCodeLogin = async (email: string, code: string): Promise<AuthResponse> => {
  // 实现验证码登录逻辑
};
```

### 3. 密码重置

```typescript
// 忘记密码
export const forgotPassword = async (email: string): Promise<void> => {
  // 实现忘记密码逻辑
};

// 重置密码
export const resetPassword = async (token: string, newPassword: string): Promise<void> => {
  // 实现重置密码逻辑
};
```