# 设备号管理和API加密功能使用指南

## 概述

本功能实现了设备号的自动生成、本地存储以及基于SM2/SM4加密算法的API请求参数组装。经过优化整理，代码结构更加清晰，功能更加完善。

### 主要功能

1. **设备号管理**：自动生成Android/iOS格式的设备号并存储到本地
2. **加密处理**：按照指定步骤对公钥和设备号进行加密处理
3. **API集成**：提供完整的API调用接口
4. **错误处理**：完善的错误处理和日志记录机制
5. **类型安全**：完整的TypeScript类型定义

## 核心功能

### 设备号生成规则

- **Android设备号格式**：`android_xxxxxxxxxxxxxxxx` (16位随机十六进制字符)
- **iOS设备号格式**：`ios_XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX` (UUID格式)
- **自动检测**：根据浏览器User-Agent自动选择生成格式
- **本地存储**：设备号生成后自动存储到localStorage，下次访问时直接使用

### 加密处理流程

按照以下5个步骤处理请求参数：

1. **Base64编码**：对主密钥（公钥）进行base64编码
2. **SM2加密**：使用SM2公钥加密base64后的数据
3. **C1C3C2处理**：对SM2加密结果进行C1C3C2模式处理
4. **添加前缀**：在处理结果前添加"04"前缀得到masterKey
5. **SM4加密**：使用主密钥的前32位作为SM4密钥加密deviceNo得到sign值

### 密钥说明

- **SM2_PUBLIC_KEY**：SM2公钥，用于加密主密钥，可以安全地存储在客户端代码中
- **MASTER_KEY**：32位随机生成的十六进制字符串，每次应用启动时重新生成，用于SM4加密
- **SM2_CIPHER_MODE**：SM2加密模式，使用C1C3C2模式（值为1）

### 随机密钥特性

- **动态生成**：每次应用启动时自动生成新的32位随机MASTER_KEY
- **格式规范**：32位大写十六进制字符串（如：A1B2C3D4E5F6789012345678ABCDEF90）
- **安全性**：每次运行使用不同的密钥，提高安全性
- **一致性**：同一次运行中所有操作使用相同的MASTER_KEY

## API接口

### 设备号管理

```typescript
import {
  getDeviceNo,
  regenerateDeviceNo,
  clearDeviceNo,
  hasDeviceNo,
  getDevicePlatform,
  validateDeviceNo
} from '../utils/device';

// 获取设备号（如果不存在则自动生成）
const deviceNo = getDeviceNo();

// 检查是否已有设备号
const exists = hasDeviceNo();

// 重新生成设备号
const newDeviceNo = regenerateDeviceNo();

// 清除设备号
clearDeviceNo();

// 获取设备平台类型
const platform = getDevicePlatform(); // 'android' | 'ios' | 'unknown'

// 验证设备号格式
const isValid = validateDeviceNo('android_1234567890abcdef');
```

### API请求参数生成

```typescript
import { buildApplyPrivateKeyRequest, applyPrivateKey } from '../api/custom-api/base-api';

// 方式1：自动使用生成的设备号
const request = buildApplyPrivateKeyRequest();

// 方式2：指定设备号
const request = buildApplyPrivateKeyRequest('custom_device_123');

// 方式3：直接调用API（自动生成设备号）
const result = await applyPrivateKey();

// 方式4：使用指定设备号调用API
const result = await applyPrivateKey('custom_device_123');
```

### 请求参数结构

```typescript
interface ApplyPrivateKeyRequest {
  deviceNo: string;    // 设备号
  masterKey: string;   // 加密后的主密钥（以04开头）
  sign: string;        // 设备号的SM4加密结果
}
```

## 使用示例

### 基本使用

```typescript
import { applyPrivateKey } from '../api/custom-api/base-api';

// 最简单的使用方式
async function basicUsage() {
  try {
    const result = await applyPrivateKey();
    console.log('申请私钥成功:', result);
  } catch (error) {
    console.error('申请私钥失败:', error);
  }
}
```

### 手动管理设备号

```typescript
import { getDeviceNo, regenerateDeviceNo } from '../utils/device';
import { buildApplyPrivateKeyRequest } from '../api/custom-api/base-api';

async function manualDeviceManagement() {
  // 获取当前设备号
  const currentDevice = getDeviceNo();
  console.log('当前设备号:', currentDevice);
  
  // 生成请求参数
  const params = buildApplyPrivateKeyRequest();
  console.log('请求参数:', params);
  
  // 重新生成设备号
  const newDevice = regenerateDeviceNo();
  console.log('新设备号:', newDevice);
}
```

### 批量处理

```typescript
async function batchProcessing() {
  const devices = ['device_001', 'device_002', 'device_003'];
  
  for (const deviceNo of devices) {
    const params = buildApplyPrivateKeyRequest(deviceNo);
    console.log(`设备 ${deviceNo} 的请求参数:`, params);
  }
}
```

## React组件集成

项目提供了一个演示组件 `DeviceDemo`，可以直接在React应用中使用：

```typescript
import { DeviceDemo } from '../components/device-demo';

function App() {
  return (
    <div>
      <DeviceDemo />
    </div>
  );
}
```

## 测试

运行测试以验证功能：

```bash
npm test -- test/base-api.test.ts
```

测试覆盖了以下场景：
- 设备号自动生成和存储
- 请求参数正确性验证
- 不同设备号生成不同签名
- 相同设备号生成一致结果
- 设备号管理功能

## 注意事项

1. **环境兼容性**：在测试环境中使用mock实现，生产环境使用真实的sm-crypto库
2. **本地存储**：设备号存储在localStorage中，清除浏览器数据会丢失设备号
3. **加密安全性**：MASTER_KEY硬编码在代码中，实际使用时应考虑安全性
4. **错误处理**：建议在实际使用中添加适当的错误处理和重试机制

## 文件结构

```
app/
├── api/custom-api/
│   ├── base-api.ts           # 核心API实现
│   └── example-usage.ts      # 使用示例
├── utils/
│   └── device.ts             # 设备号管理工具
├── components/
│   └── device-demo.tsx       # 演示组件
└── test/
    └── base-api.test.ts      # 测试文件
```

## 依赖

- `sm-crypto`: SM2/SM4加密算法实现
- `zustand`: 状态管理（用于本地存储）
- `nanoid`: 随机ID生成（项目已有依赖）
