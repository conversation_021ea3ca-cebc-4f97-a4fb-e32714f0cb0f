# NextChat Docker 部署指南

## 前置准备

1. 确保已安装 Docker 和 Docker Compose
2. 配置构建时环境变量

## 配置 API Key

### 方法一：使用环境变量文件（推荐）

1. 复制环境变量模板文件：
```bash
cp .env.local .env
```

2. 编辑 `.env` 文件，将 `sk-your-deepseek-api-key-here` 替换为你的实际 Deepseek API Key：
```bash
DEEPSEEK_API_KEY=sk-your-actual-deepseek-api-key
```

### 方法二：直接设置环境变量

```bash
export DEEPSEEK_API_KEY=sk-your-actual-deepseek-api-key
```

## 构建和部署

### 方法一：使用 Docker Compose（推荐）

```bash
# 构建并启动服务
docker-compose -f docker-compose.custom.yml up -d --build

# 查看日志
docker-compose -f docker-compose.custom.yml logs -f

# 停止服务
docker-compose -f docker-compose.custom.yml down
```

### 方法二：使用 Docker 命令

```bash
# 构建镜像（需要传递API密钥作为构建参数）
docker build -f Dockerfile.custom --build-arg DEEPSEEK_API_KEY=sk-your-actual-deepseek-api-key -t nextchat-deepseek .

# 运行容器
docker run -d -p 3000:3000 --name nextchat-deepseek nextchat-deepseek

# 查看日志
docker logs -f nextchat-deepseek

# 停止容器
docker stop nextchat-deepseek
docker rm nextchat-deepseek
```

## 访问应用

部署成功后，在浏览器中访问：http://localhost:3000

## 注意事项

1. API Key 在构建时通过环境变量传入并硬编码到镜像中，运行时无需设置环境变量
2. 确保 3000 端口未被其他服务占用
3. 如需修改端口，请同时修改 docker-compose.custom.yml 和 Docker 运行命令中的端口映射
4. 每次更换 API Key 都需要重新构建镜像
5. 构建的镜像中包含 API Key，请妥善保管镜像文件

## 生产环境部署

在生产环境中，建议：

1. 使用反向代理（如 Nginx）
2. 配置 HTTPS
3. 设置适当的防火墙规则
4. 定期备份数据