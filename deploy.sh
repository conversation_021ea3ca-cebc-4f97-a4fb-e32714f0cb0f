#!/bin/bash

# NextChat 服务器部署脚本
# 集成所有部署功能：环境检查、镜像加载、容器管理、服务验证

# 预处理帮助参数
for arg in "$@"; do
    if [ "$arg" = "-h" ] || [ "$arg" = "--help" ]; then
        # 显示帮助信息
        echo "NextChat 服务器部署脚本"
        echo
        echo "用法: $0 [选项]"
        echo
        echo "选项:"
        echo "  -h, --help                显示帮助信息"
        echo "  -f, --file FILE           指定镜像文件路径"
        echo "  -p, --port PORT           指定端口 (默认: 3000)"
        echo "  -n, --name NAME           指定容器名称 (默认: nextchat-app)"
        echo "  -s, --skip-verification   跳过部署验证"
        echo "  --no-logs                 不显示容器日志"
        echo "  --no-restart              不自动重启容器"
        echo "  --proxy-url URL           设置代理URL (解决地区限制)"
        echo "  --http-proxy URL          设置HTTP代理"
        echo "  --https-proxy URL         设置HTTPS代理"
        echo
        echo "示例:"
        echo "  $0                        # 默认部署"
        echo "  $0 -p 8080                # 指定端口"
        echo "  $0 -f image.tar.gz        # 指定镜像文件"
        echo "  $0 -n my-nextchat         # 指定容器名称"
        echo "  $0 -s --no-logs           # 静默部署"
        echo "  $0 --proxy-url http://proxy.example.com:8080  # 使用代理"
        echo
        echo "代理配置说明:"
        echo "  如果遇到 'unsupported_country_region_territory' 错误，"
        echo "  可以使用 --proxy-url 参数设置代理服务器来解决地区限制问题。"
        exit 0
    fi
done

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
DEFAULT_PORT=3000
DEFAULT_CONTAINER_NAME="nextchat-app"
DEFAULT_IMAGE_NAME="nextchat-custom:latest"
SKIP_VERIFICATION=false
AUTO_RESTART=true
SHOW_LOGS=true

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 是否安装
check_docker() {
    log_info "检查 Docker 命令是否可用..."
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        echo "安装命令:"
        echo "  Ubuntu/Debian: sudo apt-get update && sudo apt-get install docker.io"
        echo "  CentOS/RHEL: sudo yum install docker"
        echo "  macOS: brew install docker"
        exit 1
    fi
    log_info "Docker 命令可用，检查 Docker 服务状态..."
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请启动 Docker 服务"
        echo "启动命令:"
        echo "  sudo systemctl start docker"
        echo "  sudo service docker start"
        exit 1
    fi
    
    log_success "Docker 环境检查通过"
}

# 智能查找镜像文件
find_image_file() {
    local image_file=""
    local search_dirs=("./" "../" "../../" "../../../")
    
    # 如果在 dist 目录中，添加项目根目录到搜索路径
    if [[ "$(basename "$(pwd)")" == "dist" ]]; then
        search_dirs+=("../")
    fi
    
    log_info "智能搜索 Docker 镜像文件..." >&2
    
    # 搜索镜像文件
    local found_files=()
    for dir in "${search_dirs[@]}"; do
        if [ -d "$dir" ]; then
            while IFS= read -r -d '' file; do
                found_files+=("$file")
            done < <(find "$dir" -maxdepth 1 -name "nextchat*.tar.gz" -o -name "nextchat*.tar" 2>/dev/null | head -10 | tr '\n' '\0')
        fi
    done
    
    if [ ${#found_files[@]} -eq 0 ]; then
        log_error "未找到 Docker 镜像文件 (nextchat*.tar.gz 或 nextchat*.tar)" >&2
        echo "请确保镜像文件存在于以下位置之一:" >&2
        for dir in "${search_dirs[@]}"; do
            echo "  - $dir" >&2
        done
        echo "ERROR_NO_IMAGE_FILE"
        return 1
    elif [ ${#found_files[@]} -eq 1 ]; then
        image_file="${found_files[0]}"
        log_success "找到镜像文件: $image_file" >&2
    else
        log_warning "找到多个镜像文件，请选择:" >&2
        for i in "${!found_files[@]}"; do
            echo "  $((i+1)). ${found_files[$i]}" >&2
        done
        
        while true; do
            read -p "请输入选择 (1-${#found_files[@]}): " choice
            if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le ${#found_files[@]} ]; then
                image_file="${found_files[$((choice-1))]}"
                log_success "已选择: $image_file" >&2
                break
            else
                log_error "无效选择，请重新输入" >&2
            fi
        done
    fi
    
    echo "$image_file"
}

# 停止并删除旧容器
stop_old_container() {
    local container_name="$1"
    
    if docker ps -a --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        log_info "停止并删除旧容器: $container_name"
        
        if docker ps --format "table {{.Names}}" | grep -q "^${container_name}$"; then
            docker stop "$container_name" > /dev/null
            log_info "容器已停止"
        fi
        
        docker rm "$container_name" > /dev/null
        log_success "旧容器已删除"
    else
        log_info "未发现同名容器，跳过删除步骤"
    fi
}

# 删除旧镜像
remove_old_image() {
    local image_name="$1"
    
    if docker images --format "table {{.Repository}}:{{.Tag}}" | grep -q "^${image_name}$"; then
        log_info "删除旧镜像: $image_name"
        docker rmi "$image_name" > /dev/null 2>&1 || true
        log_success "旧镜像已删除"
    else
        log_info "未发现同名镜像，跳过删除步骤"
    fi
}

# 加载 Docker 镜像
load_image() {
    local image_file="$1"
    local image_name="$2"
    
    log_info "加载 Docker 镜像: $image_file" >&2
    
    # 记录加载前的镜像数量
    local images_before=$(docker images -q | wc -l)
    
    # 执行镜像加载
    local load_output
    if [[ "$image_file" == *.tar.gz ]]; then
        load_output=$(gunzip -c "$image_file" | docker load 2>&1)
    elif [[ "$image_file" == *.tar ]]; then
        load_output=$(docker load -i "$image_file" 2>&1)
    else
        log_error "不支持的镜像文件格式: $image_file" >&2
        exit 1
    fi
    
    # 检查加载命令的退出状态
    local load_status=$?
    
    # 记录加载后的镜像数量
    local images_after=$(docker images -q | wc -l)
    
    # 验证镜像是否加载成功
    if [ $load_status -eq 0 ] && echo "$load_output" | grep -q "Loaded image:"; then
        # 从输出中提取实际加载的镜像名称
        local loaded_image=$(echo "$load_output" | grep "Loaded image:" | sed 's/Loaded image: //' | head -1)
        log_success "镜像加载成功: $loaded_image" >&2
        
        # 更新镜像名称变量供后续使用
        if [ -n "$loaded_image" ]; then
            echo "$loaded_image"
            return 0
        fi
    fi
    
    # 如果上述验证失败，尝试其他验证方法
    if [ $images_after -gt $images_before ]; then
        log_success "镜像加载成功（检测到新镜像）" >&2
        # 尝试查找包含关键词的镜像
        local found_image=$(docker images --format "{{.Repository}}:{{.Tag}}" | grep -E "nextchat|latest" | head -1)
        if [ -n "$found_image" ]; then
            echo "$found_image"
            return 0
        fi
    fi
    
    log_error "镜像加载失败" >&2
    echo "加载输出: $load_output" >&2
    exit 1
}

# 启动新容器
start_container() {
    local container_name="$1"
    local image_name="$2"
    local port="$3"
    
    log_info "启动新容器: $container_name"
    
    # 检查端口是否被占用
    if netstat -tuln 2>/dev/null | grep -q ":$port " || ss -tuln 2>/dev/null | grep -q ":$port "; then
        log_warning "端口 $port 可能被占用，容器启动后请检查状态"
    fi
    
    # 构建环境变量参数
    local env_args="-e NODE_ENV=production"
    
    # 添加代理配置支持（如果环境变量存在）
    if [ -n "$PROXY_URL" ]; then
        env_args="$env_args -e PROXY_URL=$PROXY_URL"
        log_info "使用代理配置: $PROXY_URL"
    fi
    
    if [ -n "$HTTP_PROXY" ]; then
        env_args="$env_args -e HTTP_PROXY=$HTTP_PROXY"
        log_info "使用HTTP代理: $HTTP_PROXY"
    fi
    
    if [ -n "$HTTPS_PROXY" ]; then
        env_args="$env_args -e HTTPS_PROXY=$HTTPS_PROXY"
        log_info "使用HTTPS代理: $HTTPS_PROXY"
    fi
    
    # 添加 Stripe 配置支持（如果环境变量存在）
    if [ -n "$STRIPE_SECRET_KEY" ]; then
        env_args="$env_args -e STRIPE_SECRET_KEY=$STRIPE_SECRET_KEY"
        log_info "使用Stripe密钥配置"
    fi
    
    if [ -n "$NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY" ]; then
        env_args="$env_args -e NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=$NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY"
        log_info "使用Stripe公钥配置"
    fi
    
    # 启动容器
    eval "docker run -d \
        --name '$container_name' \
        --restart unless-stopped \
        -p '$port:3000' \
        $env_args \
        '$image_name'"
    
    log_success "容器启动成功"
}

# 验证部署状态
verify_deployment() {
    local container_name="$1"
    local port="$2"
    
    if [ "$SKIP_VERIFICATION" = "true" ]; then
        log_info "跳过部署验证"
        return 0
    fi
    
    log_info "验证部署状态..."
    
    # 等待容器启动
    sleep 3
    
    # 检查容器状态
    if ! docker ps --format "table {{.Names}}\t{{.Status}}" | grep "$container_name" | grep -q "Up"; then
        log_error "容器未正常运行"
        if [ "$SHOW_LOGS" = "true" ]; then
            log_info "容器日志:"
            docker logs "$container_name" --tail 20
        fi
        return 1
    fi
    
    # 检查端口监听
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if netstat -tuln 2>/dev/null | grep -q ":$port " || ss -tuln 2>/dev/null | grep -q ":$port "; then
            log_success "服务已在端口 $port 上启动"
            return 0
        fi
        
        log_info "等待服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_warning "服务可能未完全启动，请手动检查"
    return 1
}

# 显示部署成功信息
show_success_info() {
    local container_name="$1"
    local port="$2"
    
    log_success "NextChat 部署成功！"
    echo
    log_info "🌐 访问地址:"
    echo "  本地访问: http://localhost:$port"
    
    # 获取服务器 IP
    local server_ip=$(hostname -I 2>/dev/null | awk '{print $1}' || ip route get 1 2>/dev/null | awk '{print $7; exit}' || echo "YOUR_SERVER_IP")
    if [ "$server_ip" != "YOUR_SERVER_IP" ] && [ -n "$server_ip" ]; then
        echo "  网络访问: http://$server_ip:$port"
    else
        echo "  网络访问: http://YOUR_SERVER_IP:$port"
    fi
    echo
    
    log_info "📋 管理命令:"
    echo "  查看状态: docker ps | grep $container_name"
    echo "  查看日志: docker logs $container_name -f"
    echo "  重启服务: docker restart $container_name"
    echo "  停止服务: docker stop $container_name"
    echo "  删除容器: docker rm -f $container_name"
    echo
    
    if [ "$SHOW_LOGS" = "true" ]; then
        log_info "📝 最近日志:"
        docker logs "$container_name" --tail 10 2>/dev/null || log_warning "无法获取容器日志"
        echo
    fi
}

# 显示帮助信息
show_help() {
    echo "NextChat 服务器部署脚本" >&2
    echo >&2
    echo "用法: $0 [选项]" >&2
    echo >&2
    echo "选项:" >&2
    echo "  -h, --help                显示帮助信息" >&2
    echo "  -f, --file FILE           指定镜像文件路径" >&2
    echo "  -p, --port PORT           指定端口 (默认: $DEFAULT_PORT)" >&2
    echo "  -n, --name NAME           指定容器名称 (默认: $DEFAULT_CONTAINER_NAME)" >&2
    echo "  -s, --skip-verification   跳过部署验证" >&2
    echo "  --no-logs                 不显示容器日志" >&2
    echo "  --no-restart              不自动重启容器" >&2
    echo "  --proxy-url URL           设置代理URL (解决地区限制)" >&2
    echo "  --http-proxy URL          设置HTTP代理" >&2
    echo "  --https-proxy URL         设置HTTPS代理" >&2
    echo >&2
    echo "示例:" >&2
    echo "  $0                        # 默认部署" >&2
    echo "  $0 -p 8080                # 指定端口" >&2
    echo "  $0 -f image.tar.gz        # 指定镜像文件" >&2
    echo "  $0 -n my-nextchat         # 指定容器名称" >&2
    echo "  $0 -s --no-logs           # 静默部署" >&2
    echo "  $0 --proxy-url http://proxy.example.com:8080  # 使用代理" >&2
    echo >&2
    echo "代理配置说明:" >&2
    echo "  如果遇到 'unsupported_country_region_territory' 错误，" >&2
    echo "  可以使用 --proxy-url 参数设置代理服务器来解决地区限制问题。" >&2
}

# 解析命令行参数
parse_args() {
    local image_file=""
    local port="$DEFAULT_PORT"
    local container_name="$DEFAULT_CONTAINER_NAME"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--file)
                image_file="$2"
                shift 2
                ;;
            -p|--port)
                port="$2"
                shift 2
                ;;
            -n|--name)
                container_name="$2"
                shift 2
                ;;
            -s|--skip-verification)
                SKIP_VERIFICATION=true
                shift
                ;;
            --no-logs)
                SHOW_LOGS=false
                shift
                ;;
            --no-restart)
                AUTO_RESTART=false
                shift
                ;;
            --proxy-url)
                export PROXY_URL="$2"
                shift 2
                ;;
            --http-proxy)
                export HTTP_PROXY="$2"
                shift 2
                ;;
            --https-proxy)
                export HTTPS_PROXY="$2"
                shift 2
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 验证端口号
    if ! [[ "$port" =~ ^[0-9]+$ ]] || [ "$port" -lt 1 ] || [ "$port" -gt 65535 ]; then
        log_error "无效端口号: $port"
        exit 1
    fi
    
    # 如果未指定镜像文件，智能查找
    if [ -z "$image_file" ]; then
        image_file=$(find_image_file)
        if [ "$image_file" = "ERROR_NO_IMAGE_FILE" ]; then
            exit 1
        fi
    elif [ ! -f "$image_file" ]; then
        log_error "镜像文件不存在: $image_file"
        exit 1
    fi
    
    echo "$image_file|$port|$container_name"
}

# 加载环境变量配置
load_env_config() {
    # 查找环境变量文件
    local env_file=""
    if [ -f ".env.local" ]; then
        env_file=".env.local"
    elif [ -f ".env" ]; then
        env_file=".env"
    fi
    
    if [ -n "$env_file" ]; then
        log_info "从 $env_file 加载环境变量配置..."
        
        # 读取代理配置（如果未通过命令行设置）
        if [ -z "$PROXY_URL" ]; then
            PROXY_URL=$(grep "^PROXY_URL=" "$env_file" 2>/dev/null | cut -d '=' -f2- | sed 's/^["'\'']*//;s/["'\'']*$//' || echo "")
        fi
        
        if [ -z "$HTTP_PROXY" ]; then
            HTTP_PROXY=$(grep "^HTTP_PROXY=" "$env_file" 2>/dev/null | cut -d '=' -f2- | sed 's/^["'\'']*//;s/["'\'']*$//' || echo "")
        fi
        
        if [ -z "$HTTPS_PROXY" ]; then
            HTTPS_PROXY=$(grep "^HTTPS_PROXY=" "$env_file" 2>/dev/null | cut -d '=' -f2- | sed 's/^["'\'']*//;s/["'\'']*$//' || echo "")
        fi
        
        # 读取 Stripe 配置
        STRIPE_SECRET_KEY=$(grep "^STRIPE_SECRET_KEY=" "$env_file" 2>/dev/null | cut -d '=' -f2- | sed 's/^["'\'']*//;s/["'\'']*$//' || echo "")
        NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=$(grep "^NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=" "$env_file" 2>/dev/null | cut -d '=' -f2- | sed 's/^["'\'']*//;s/["'\'']*$//' || echo "")
        
        # 导出环境变量
        [ -n "$PROXY_URL" ] && export PROXY_URL
        [ -n "$HTTP_PROXY" ] && export HTTP_PROXY
        [ -n "$HTTPS_PROXY" ] && export HTTPS_PROXY
        [ -n "$STRIPE_SECRET_KEY" ] && export STRIPE_SECRET_KEY
        [ -n "$NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY" ] && export NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
        
        # 显示代理配置状态
        if [ -n "$PROXY_URL" ] || [ -n "$HTTP_PROXY" ] || [ -n "$HTTPS_PROXY" ]; then
            log_success "检测到代理配置:"
            [ -n "$PROXY_URL" ] && echo "  PROXY_URL: $PROXY_URL"
            [ -n "$HTTP_PROXY" ] && echo "  HTTP_PROXY: $HTTP_PROXY"
            [ -n "$HTTPS_PROXY" ] && echo "  HTTPS_PROXY: $HTTPS_PROXY"
        else
            log_info "未配置代理，如遇地区限制请在 $env_file 文件中添加代理配置"
        fi
        
        # 显示 Stripe 配置状态
        if [ -n "$STRIPE_SECRET_KEY" ] || [ -n "$NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY" ]; then
            log_success "检测到Stripe支付配置:"
            [ -n "$STRIPE_SECRET_KEY" ] && echo "  STRIPE_SECRET_KEY: [已配置]"
            [ -n "$NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY" ] && echo "  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: [已配置]"
        fi
    else
        log_warning "未找到环境变量文件 (.env.local 或 .env)"
        log_info "如需配置代理，请创建 .env.local 文件并添加代理设置"
    fi
    log_info "环境变量配置加载完成"
}

# 主函数
main() {
    log_info "开始 NextChat 服务器部署流程..."
    
    # 解析参数
    local args_result
    args_result=$(parse_args "$@")
    IFS='|' read -r image_file port container_name <<< "$args_result"
    
    # 加载环境变量配置
    load_env_config
    
    log_info "部署配置:"
    echo "  镜像文件: $image_file"
    echo "  容器名称: $container_name"
    echo "  服务端口: $port"
    echo "  镜像名称: $DEFAULT_IMAGE_NAME"
    echo
    
    # 1. 环境检查
    log_info "1. 检查 Docker 环境..."
    check_docker
    
    # 2. 停止旧容器
    log_info "2. 停止旧容器..."
    stop_old_container "$container_name"
    
    # 3. 删除旧镜像
    log_info "3. 删除旧镜像..."
    remove_old_image "$DEFAULT_IMAGE_NAME"
    
    # 4. 加载新镜像
    log_info "4. 加载新镜像..."
    local actual_image_name
    actual_image_name=$(load_image "$image_file" "$DEFAULT_IMAGE_NAME")
    
    # 如果load_image返回了实际的镜像名称，使用它；否则使用默认名称
    if [ -n "$actual_image_name" ]; then
        DEFAULT_IMAGE_NAME="$actual_image_name"
    fi
    
    # 5. 启动新容器
    log_info "5. 启动新容器..."
    start_container "$container_name" "$DEFAULT_IMAGE_NAME" "$port"
    
    # 6. 验证部署
    log_info "6. 验证部署状态..."
    if verify_deployment "$container_name" "$port"; then
        show_success_info "$container_name" "$port"
    else
        log_warning "部署可能存在问题，请手动检查"
        if [ "$SHOW_LOGS" = "true" ]; then
            log_info "容器日志:"
            docker logs "$container_name" --tail 20 2>/dev/null || log_warning "无法获取容器日志"
        fi
    fi
}

# 执行主函数
main "$@"