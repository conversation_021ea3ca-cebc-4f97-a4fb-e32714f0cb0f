// SEO配置文件
export const SEO_CONFIG = {
  // 网站基本信息
  SITE_NAME: "创想AI",
  SITE_URL: "https://www.51creativeai.com",
  SITE_DESCRIPTION:
    "创想AI - 全球领先的AI智能助手平台，集成GPT-4、Claude、DeepSeek等顶级AI模型。提供专业级AI对话、代码生成、创意写作、数据分析等服务，助力个人与企业数字化转型。",

  // 关键词
  KEYWORDS: [
    "创想AI",
    "Creative AI",
    "AI智能助手",
    "GPT-4",
    "Claude AI",
    "DeepSeek",
    "AI对话平台",
    "智能编程助手",
    "AI创意写作",
    "数据分析AI",
    "企业AI解决方案",
    "多模态AI",
    "AI工作流",
    "智能客服系统",
    "AI内容生成",
    "机器学习平台",
    "人工智能服务",
  ],

  // 社交媒体
  SOCIAL: {
    twitter: "@51CreativeAI",
    github: "https://github.com/51CreativeAI",
  },

  // 作者信息
  AUTHOR: {
    name: "创想AI团队",
    email: "<EMAIL>",
  },

  // 图片
  IMAGES: {
    og: "/og-image.png",
    logo: "/logo.png",
    favicon: "/favicon.ico",
  },

  // 语言支持
  LANGUAGES: {
    "zh-CN": "中文",
    "en-US": "English",
    "ja-JP": "日本語",
    "ko-KR": "한국어",
  },

  // 结构化数据
  STRUCTURED_DATA: {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    name: "创想AI",
    description:
      "全球领先的AI智能助手平台，集成多种顶级AI模型，提供专业级AI服务",
    url: "https://www.51creativeai.com",
    applicationCategory: "ProductivityApplication",
    operatingSystem: "Any",
    offers: {
      "@type": "Offer",
      price: "0",
      priceCurrency: "CNY",
    },
    creator: {
      "@type": "Organization",
      name: "创想AI团队",
      url: "https://www.51creativeai.com",
    },
    featureList: [
      "GPT-4智能对话",
      "Claude AI助手",
      "DeepSeek编程",
      "多模态AI交互",
      "企业级AI解决方案",
      "AI工作流自动化",
      "创意内容生成",
      "数据智能分析",
    ],
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.9",
      ratingCount: "2580",
      bestRating: "5",
      worstRating: "1",
    },
  },
};

// 生成页面特定的SEO配置
export function generatePageSEO({
  title,
  description,
  keywords = [],
  path = "/",
  image,
}: {
  title: string;
  description: string;
  keywords?: string[];
  path?: string;
  image?: string;
}) {
  const fullTitle = title.includes(SEO_CONFIG.SITE_NAME)
    ? title
    : `${title} | ${SEO_CONFIG.SITE_NAME}`;

  const url = `${SEO_CONFIG.SITE_URL}${path}`;
  const ogImage = image || SEO_CONFIG.IMAGES.og;

  return {
    title: fullTitle,
    description,
    keywords: [...SEO_CONFIG.KEYWORDS, ...keywords],
    openGraph: {
      title: fullTitle,
      description,
      url,
      siteName: SEO_CONFIG.SITE_NAME,
      images: [
        {
          url: ogImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: "zh_CN",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: fullTitle,
      description,
      images: [ogImage],
      creator: SEO_CONFIG.SOCIAL.twitter,
    },
    alternates: {
      canonical: url,
    },
  };
}

// 生成结构化数据
export function generateStructuredData(type: string, data: any) {
  const baseData = {
    "@context": "https://schema.org",
    "@type": type,
    ...data,
  };

  return JSON.stringify(baseData);
}

// 常用的结构化数据模板
export const STRUCTURED_DATA_TEMPLATES = {
  WebApplication: (data: any) =>
    generateStructuredData("WebApplication", {
      ...SEO_CONFIG.STRUCTURED_DATA,
      ...data,
    }),

  Article: (data: any) =>
    generateStructuredData("Article", {
      publisher: {
        "@type": "Organization",
        name: SEO_CONFIG.SITE_NAME,
        url: SEO_CONFIG.SITE_URL,
      },
      ...data,
    }),

  FAQPage: (faqs: Array<{ question: string; answer: string }>) =>
    generateStructuredData("FAQPage", {
      mainEntity: faqs.map((faq) => ({
        "@type": "Question",
        name: faq.question,
        acceptedAnswer: {
          "@type": "Answer",
          text: faq.answer,
        },
      })),
    }),
};
