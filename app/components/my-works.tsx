"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import { useNavigate } from "react-router-dom";
import styles from "./my-works.module.scss";
import { showToast } from "./ui-lib";
import { Path } from "../constant";
import {
  queryOperateRecordPage,
  queryImageByPromptId,
  QueryOperateRecordPageRequest,
  OperateRecord,
  WorkStatus,
  OperateType,
} from "../api/custom-api/ai-tool-api";
import { isAuthenticated } from "../utils/auth-token";

// 我的作品页面组件
export function MyWorksPage() {
  const navigate = useNavigate();

  // 检查用户是否已登录
  useEffect(() => {
    if (!isAuthenticated()) {
      navigate(Path.Login);
      return;
    }
  }, [navigate]);

  const [works, setWorks] = useState<OperateRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    pageNo: 1,
    pageSize: 12,
    total: 0,
  });
  const [jumpToPage, setJumpToPage] = useState<string>("");
  const [updatingWorks, setUpdatingWorks] = useState<Set<string>>(new Set());
  const [updateResults, setUpdateResults] = useState<Map<string, 'success' | 'failed' | 'processing'>>(new Map());

  // 防抖处理的ref
  const debounceTimers = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // 获取状态显示文本
  const getStatusText = (status: WorkStatus) => {
    switch (status) {
      case WorkStatus.PROCESSING:
        return "处理中";
      case WorkStatus.SUCCESS:
        return "已完成";
      case WorkStatus.FAILED:
        return "失败";
      case WorkStatus.REQUEST_FAILED:
        return "请求失败";
      default:
        return "未知";
    }
  };

  // 获取状态样式类名
  const getStatusClassName = (status: WorkStatus) => {
    switch (status) {
      case WorkStatus.PROCESSING:
        return styles.statusProcessing;
      case WorkStatus.SUCCESS:
        return styles.statusSuccess;
      case WorkStatus.FAILED:
      case WorkStatus.REQUEST_FAILED:
        return styles.statusFailed;
      default:
        return styles.statusDefault;
    }
  };

  // 获取操作类型文本
  const getOperateTypeText = (type: OperateType) => {
    switch (type) {
      case OperateType.CAT_VTON:
        return "换装";
      case OperateType.TEXT_TO_IMAGE:
        return "文生图";
      case OperateType.IMAGE_TO_IMAGE:
        return "图生图";
      case OperateType.ID_PHOTO:
        return "证件照";
      case OperateType.IMAGE_ENHANCE:
        return "图片放大";
      case OperateType.LINE_DRAWING:
        return "线稿生成";
      case OperateType.FLUX_EXPAND:
        return "图片扩展";
      case OperateType.FLUX_IMAGE_INPAINT:
        return "图片重绘";
      case OperateType.FLUX_ANYTHING_REPLACE:
        return "万物替换";
      case OperateType.REMOVE_ANYTHING:
        return "删除元素";
      case OperateType.FACE_SWAP:
        return "换脸";
      case OperateType.POSTER:
        return "海报生成";
      case OperateType.CARTOON:
        return "动漫风格";
      case OperateType.REMOVE_WATERMARK:
        return "去除水印";
      default:
        return "未知操作";
    }
  };

  // 格式化创建时间
  const formatCreatedTime = (timeString: string) => {
    try {
      const date = new Date(timeString);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMinutes = Math.floor(diffMs / (1000 * 60));

      if (diffMinutes < 1) {
        return "刚刚";
      } else if (diffMinutes < 60) {
        return `${diffMinutes}分钟前`;
      } else if (diffHours < 24) {
        return `${diffHours}小时前`;
      } else if (diffDays < 7) {
        return `${diffDays}天前`;
      } else {
        return date.toLocaleDateString("zh-CN", {
          year: "numeric",
          month: "short",
          day: "numeric",
        });
      }
    } catch (error) {
      return timeString;
    }
  };

  // 加载作品列表
  const loadWorks = async (pageNo: number = 1) => {
    try {
      setIsLoading(true);
      setError(null);

      const request: QueryOperateRecordPageRequest = {
        pageNo,
        pageSize: pagination.pageSize,
      };

      const response = await queryOperateRecordPage(request);

      console.log("response", response);

      setWorks(response.rows || []);
      setPagination((prev) => ({
        ...prev,
        pageNo: pageNo, // 使用请求的页码，确保一致性
        total: response.totalRows || 0,
      }));
    } catch (error: any) {
      console.error("Load works error:", error);
      setError(error.message || "加载失败，请稍后重试");
      setWorks([]);
    } finally {
      setIsLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    loadWorks();
  }, []);

  // 刷新数据
  const handleRefresh = async () => {
    try {
      await loadWorks(1);
      showToast("刷新成功！");
    } catch (error) {
      console.error("刷新失败:", error);
      showToast("刷新失败，请重试");
    }
  };

  // 页面跳转
  const handlePageChange = (pageNo: number) => {
    loadWorks(pageNo);
  };

  // 处理跳转到指定页面
  const handleJumpToPage = () => {
    const pageNum = parseInt(jumpToPage);
    if (!isNaN(pageNum) && pageNum >= 1 && pageNum <= totalPages) {
      handlePageChange(pageNum);
      setJumpToPage("");
    } else {
      showToast("请输入有效的页码");
    }
  };

  // 处理输入框回车事件
  const handleJumpInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleJumpToPage();
    }
  };

  // 查看大图
  const handleViewImage = (imageUrl: string) => {
    if (!imageUrl) {
      showToast("图片不存在");
      return;
    }
    // 在新窗口打开图片
    window.open(imageUrl, "_blank");
  };

  // 手动更新单个作品状态
  const handleManualUpdate = useCallback(async (promptId: string) => {
    if (!promptId) return;

    // 清除之前的防抖定时器
    const existingTimer = debounceTimers.current.get(promptId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // 设置新的防抖定时器
    const timer = setTimeout(async () => {
      try {
        setUpdatingWorks((prev) => new Set(prev).add(promptId));
        setUpdateResults((prev) => new Map(prev).set(promptId, 'processing'));

        const result = await queryImageByPromptId({ prompt_id: promptId });

        console.log("result", result, promptId);

        // 只要接口调用成功就显示更新成功
        if (result?.imageUrl) {
          // 如果有imageUrl，更新作品状态
          setWorks((prevWorks) =>
            prevWorks.map((work) =>
              work.promptId === promptId
                ? {
                    ...work,
                    workStatus: WorkStatus.SUCCESS,
                    resultImageUrl: result.imageUrl!,
                  }
                : work,
            ),
          );
        }

        // 接口调用成功就显示成功状态
        setUpdateResults((prev) => new Map(prev).set(promptId, 'success'));

        // 2秒后清除成功状态
        setTimeout(() => {
          setUpdateResults((prev) => {
            const newMap = new Map(prev);
            newMap.delete(promptId);
            return newMap;
          });
        }, 1500);
      } catch (error) {
        console.error("手动更新失败:", error);
        setUpdateResults((prev) => new Map(prev).set(promptId, 'failed'));

        // 2秒后清除失败状态
        setTimeout(() => {
          setUpdateResults((prev) => {
            const newMap = new Map(prev);
            newMap.delete(promptId);
            return newMap;
          });
        }, 1500);
      } finally {
        setUpdatingWorks((prev) => {
          const newSet = new Set(prev);
          newSet.delete(promptId);
          return newSet;
        });
        debounceTimers.current.delete(promptId);
      }
    }, 500); // 500毫秒防抖

    debounceTimers.current.set(promptId, timer);
  }, []);

  // 计算总页数
  const totalPages = Math.ceil(pagination.total / pagination.pageSize);

  if (isLoading) {
    return (
      <div className={styles.worksPage}>
        <div className={styles.pageHeader}>
          <div className={styles.headerContent}>
            <div className={styles.headerLeft}>
              <h1>我的作品</h1>
              <p>查看您的所有创作记录</p>
            </div>
            <div className={styles.headerRight}>
              <div className={styles.closeButton} onClick={() => navigate(-1)}>
                ✕
              </div>
            </div>
          </div>
        </div>
        <div className={styles.pageContent}>
          <div className={styles.contentContainer}>
            <div className={styles.loading}>
              <div className={styles.loadingSpinner}></div>
              <div className={styles.loadingText}>正在加载作品...</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.worksPage}>
      <div className={styles.pageHeader}>
        <div className={styles.headerContent}>
          <div className={styles.headerLeft}>
            <h1>我的作品</h1>
            <p>查看您的所有创作记录</p>
          </div>
          <div className={styles.headerRight}>
            <button
              className={styles.refreshButton}
              onClick={handleRefresh}
              disabled={isLoading}
            >
              {isLoading ? "刷新中..." : "刷新"}
            </button>
            <div className={styles.closeButton} onClick={() => navigate(-1)}>
              ✕
            </div>
          </div>
        </div>
      </div>

      <div className={styles.pageContent}>
        <div className={styles.contentContainer}>
          {/* 顶部提示信息 */}
          <div className={styles.noticeBar}>
            <div className={styles.noticeIcon}>✨</div>
            <div className={styles.noticeText}>
              服务火爆，结果推送可能延迟，可手动刷新
            </div>
          </div>

          {error && (
            <div className={styles.errorMessage}>
              <div className={styles.errorIcon}>⚠️</div>
              <div className={styles.errorText}>{error}</div>
              <button className={styles.retryButton} onClick={handleRefresh}>
                重试
              </button>
            </div>
          )}

          {!error && works.length === 0 && (
            <div className={styles.emptyState}>
              <div className={styles.emptyIcon}>✨</div>
              <div className={styles.emptyTitle}>暂无作品</div>
              <div className={styles.emptySubtitle}>开始您的创作之旅</div>
              <button
                className={styles.createButton}
                onClick={() => navigate(Path.ImageEdit)}
              >
                立即创作
              </button>
            </div>
          )}

          {!error && works.length > 0 && (
            <>
              {/* 作品网格 */}
              <div className={styles.worksGrid}>
                {works.map((work) => (
                  <div key={work.id} className={styles.workCard}>
                    <div className={styles.workImage}>
                      {work.workStatus === WorkStatus.SUCCESS &&
                      work.resultImageUrl ? (
                        <>
                          <img
                            src={work.resultImageUrl}
                            alt="作品"
                            onClick={() => handleViewImage(work.resultImageUrl)}
                          />
                          <div className={styles.workOverlay}>
                            <button
                              className={styles.viewButton}
                              onClick={() =>
                                handleViewImage(work.resultImageUrl)
                              }
                            >
                              查看大图
                            </button>
                          </div>
                        </>
                      ) : (
                        <div
                          className={`${styles.statusDisplay} ${
                            work.workStatus === WorkStatus.PROCESSING
                              ? styles.processing
                              : work.workStatus === WorkStatus.FAILED ||
                                work.workStatus === WorkStatus.REQUEST_FAILED
                              ? styles.failed
                              : ""
                          }`}
                        >
                          {/* <div className={`${styles.statusIcon} ${
                            work.workStatus === WorkStatus.PROCESSING ? styles.processing :
                            (work.workStatus === WorkStatus.FAILED || work.workStatus === WorkStatus.REQUEST_FAILED) ? styles.failed : ''
                          }`}>
                            {work.workStatus === WorkStatus.PROCESSING && "⟳"}
                            {work.workStatus === WorkStatus.FAILED && "✕"}
                            {work.workStatus === WorkStatus.REQUEST_FAILED && "⚠"}
                          </div> */}
                          {work.workStatus === WorkStatus.PROCESSING && (
                            <div className={styles.processingHint}>
                              <div className={styles.processingText}>
                                创作中
                              </div>
                              <button
                                className={`${styles.manualUpdateButton} ${
                                  updateResults.get(work.promptId!) === 'success' ? styles.success :
                                  updateResults.get(work.promptId!) === 'failed' ? styles.failed : ''
                                }`}
                                onClick={() =>
                                  handleManualUpdate(work.promptId!)
                                }
                                disabled={updatingWorks.has(work.promptId!) || updateResults.has(work.promptId!)}
                              >
                                {(() => {
                                  const result = updateResults.get(work.promptId!);
                                  if (updatingWorks.has(work.promptId!)) return "刷新中";
                                  if (result === 'success') return "已更新";
                                  if (result === 'failed') return "更新失败";
                                  return "刷新";
                                })()}
                              </button>
                            </div>
                          )}
                          {(work.workStatus === WorkStatus.FAILED ||
                            work.workStatus === WorkStatus.REQUEST_FAILED) && (
                            <div className={styles.failedHint}>创作失败</div>
                          )}
                        </div>
                      )}
                    </div>
                    <div className={styles.workInfo}>
                      <div className={styles.workMeta}>
                        <span className={styles.workType}>
                          {getOperateTypeText(work.operateType)}
                        </span>
                        <span
                          className={`${styles.workStatus} ${getStatusClassName(
                            work.workStatus,
                          )}`}
                        >
                          {getStatusText(work.workStatus)}
                        </span>
                      </div>
                      <div className={styles.workDetails}>
                        <div className={styles.workTime}>
                          {formatCreatedTime(work.createTime)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 分页 */}
              {pagination.total > 0 && (
                <div className={styles.pagination}>
                  <button
                    className={styles.pageButton}
                    onClick={() => handlePageChange(pagination.pageNo - 1)}
                    disabled={pagination.pageNo <= 1}
                  >
                    上一页
                  </button>

                  <div className={styles.pageInfo}>
                    <span className={styles.pageNumbers}>
                      第 {pagination.pageNo} 页，共 {totalPages} 页
                    </span>
                    <span className={styles.totalCount}>
                      共 {pagination.total} 条记录
                    </span>
                  </div>

                  <div className={styles.jumpToPage}>
                    <span className={styles.jumpLabel}>跳转到</span>
                    <input
                      type="number"
                      min="1"
                      max={totalPages}
                      value={jumpToPage}
                      onChange={(e) => setJumpToPage(e.target.value)}
                      onKeyDown={handleJumpInputKeyPress}
                      className={styles.jumpInput}
                      placeholder="页码"
                    />
                    <button
                      className={styles.jumpButton}
                      onClick={handleJumpToPage}
                      disabled={!jumpToPage}
                    >
                      跳转
                    </button>
                  </div>

                  <button
                    className={styles.pageButton}
                    onClick={() => handlePageChange(pagination.pageNo + 1)}
                    disabled={pagination.pageNo >= totalPages}
                  >
                    下一页
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
