"use client";

import React, { useState, useEffect } from "react";
import {
  Elements,
  PaymentElement,
  ExpressCheckoutElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import styles from "./payment-element.module.scss";
import { RechargePackage, StripeApi } from "../api/custom-api";
import {
  AddressCollection,
  AddressInfo,
  validateAddress,
  detectUserLocation,
} from "./address-collection";

// Stripe 公钥 - 从环境变量获取
const stripePromise = loadStripe(
  process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!,
);

interface PaymentFormProps {
  clientSecret: string;
  selectedPackage: RechargePackage;
  addressInfo: AddressInfo;
  onSuccess: () => void;
  onError: (error: string) => void;
  onCancel: () => void;
}

// 支付表单组件
function PaymentForm({
  clientSecret,
  selectedPackage,
  addressInfo,
  onSuccess,
  onError,
  onCancel,
}: PaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);
    setMessage(null);

    // 在提交前验证表单
    const { error: submitError } = await elements.submit();
    if (submitError) {
      setMessage(submitError.message || "请检查您的支付信息");
      onError(submitError.message || "请检查您的支付信息");
      setIsLoading(false);
      return;
    }

    try {
      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/#/recharge?success=true`,
          payment_method_data: {
            billing_details: {
              address: {
                country: addressInfo.country,
                postal_code: addressInfo.postalCode,
              },
            },
          },
        },
        redirect: "if_required", // 避免不必要的重定向
      });

      if (error) {
        let errorMessage = "支付失败";

        if (error.type === "card_error") {
          // 处理银行卡相关错误
          switch (error.code) {
            case "incomplete_number":
              errorMessage = "银行卡号不完整，请检查卡号";
              break;
            case "incomplete_cvc":
              errorMessage = "安全码不完整，请检查CVC";
              break;
            case "incomplete_expiry":
              errorMessage = "有效期不完整，请检查月份和年份";
              break;
            case "invalid_number":
              errorMessage = "银行卡号无效，请检查卡号";
              break;
            case "invalid_expiry_month":
              errorMessage = "有效期月份无效";
              break;
            case "invalid_expiry_year":
              errorMessage = "有效期年份无效";
              break;
            case "invalid_cvc":
              errorMessage = "安全码无效";
              break;
            case "card_declined":
              errorMessage = "银行卡被拒绝，请联系您的银行或使用其他卡片";
              break;
            case "insufficient_funds":
              errorMessage = "余额不足，请检查账户余额";
              break;
            default:
              errorMessage = error.message || "银行卡验证失败";
          }
        } else if (error.type === "validation_error") {
          errorMessage = error.message || "请检查您的支付信息";
        } else {
          errorMessage = "支付过程中发生意外错误";
        }

        setMessage(errorMessage);
        onError(errorMessage);
      } else if (paymentIntent && paymentIntent.status === "succeeded") {
        // 支付成功
        onSuccess();
      }
    } catch (err: any) {
      setMessage(err.message || "支付失败");
      onError(err.message || "支付失败");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className={styles["payment-form"]}>
      <div className={styles["payment-header"]}>
        <h3 className={styles["payment-title"]}>完成支付</h3>
        <div className={styles["package-summary"]}>
          <div className={styles["package-name"]}>{selectedPackage.title}</div>
          <div className={styles["package-details"]}>
            <div className={styles["package-coins"]}>
              {selectedPackage.coins} 创想值
            </div>
            <div className={styles["package-price"]}>
              ${selectedPackage.price}
            </div>
          </div>
        </div>
      </div>

      <div className={styles["payment-content"]}>
        {/* 显示收集到的地址信息 */}
        <div className={styles["address-summary"]}>
          <h4 className={styles["address-title"]}>账单地址</h4>
          <div className={styles["address-info"]}>
            <div className={styles["address-item"]}>
              <span className={styles["address-label"]}>国家:</span>
              <span className={styles["address-value"]}>
                {addressInfo.country}
              </span>
            </div>
            <div className={styles["address-item"]}>
              <span className={styles["address-label"]}>邮编:</span>
              <span className={styles["address-value"]}>
                {addressInfo.postalCode}
              </span>
            </div>
          </div>
        </div>

        <div className={styles["payment-element-container"]}>
          {/* Express Checkout Element - 用于显示一键支付按钮（Google Pay, Apple Pay等） */}
          <div className={styles["express-checkout-container"]}>
            <ExpressCheckoutElement
              options={{
                paymentMethods: {
                  googlePay: "always", // 强制显示Google Pay按钮，即使没有绑卡
                  applePay: "auto", // 自动显示Apple Pay按钮（如果设备支持）
                  link: "auto", // 显示Link按钮
                },
                paymentMethodOrder: ["googlePay", "applePay", "link"], // Google Pay显示在第一位
                layout: {
                  maxColumns: 1,
                  maxRows: 1,
                  overflow: "auto",
                },
              }}
              onConfirm={async (event) => {
                const { error: submitError } = await elements!.submit();
                if (submitError) {
                  setMessage(submitError.message || "请检查您的支付信息");
                  onError(submitError.message || "请检查您的支付信息");
                  return;
                }

                const { error } = await stripe!.confirmPayment({
                  elements: elements!,
                  confirmParams: {
                    return_url: `${window.location.origin}/#/recharge?success=true`,
                    payment_method_data: {
                      billing_details: {
                        address: {
                          country: addressInfo.country,
                          postal_code: addressInfo.postalCode,
                        },
                      },
                    },
                  },
                  redirect: "if_required",
                });

                if (error) {
                  setMessage(error.message || "支付失败");
                  onError(error.message || "支付失败");
                } else {
                  onSuccess();
                }
              }}
              onCancel={() => console.log("Express checkout cancelled")}
            />
          </div>

          {/* 分隔线 */}
          <div className={styles["payment-divider"]}>
            <span>或</span>
          </div>

          {/* 传统支付表单 */}
          <PaymentElement
            options={{
              layout: "tabs",
              paymentMethodOrder: ["card"],
              wallets: {
                applePay: "never", // 禁用Apple Pay，避免与Express Checkout重复
                googlePay: "never", // 禁用Google Pay，避免与Express Checkout重复
                link: "never", // 禁用Link，只在Express Checkout中显示
              },
              fields: {
                billingDetails: {
                  address: "never", // 完全隐藏地址收集字段
                  email: "auto",
                  name: "auto",
                  phone: "never",
                },
              },
              terms: {
                card: "never",
              },
            }}
          />
        </div>

        {message && <div className={styles["error-message"]}>{message}</div>}
      </div>

      <div className={styles["payment-actions"]}>
        <button
          type="button"
          className={styles["cancel-button"]}
          onClick={onCancel}
          disabled={isLoading}
        >
          取消
        </button>
        <button
          type="submit"
          className={styles["pay-button"]}
          disabled={!stripe || isLoading}
        >
          {isLoading ? (
            <>
              <span className={styles["loading-spinner"]}></span>
              处理中...
            </>
          ) : (
            `支付 $${selectedPackage.price}`
          )}
        </button>
      </div>

      <div className={styles["security-info"]}>
        <svg
          className={styles["security-icon"]}
          viewBox="0 0 24 24"
          fill="currentColor"
        >
          <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11H16V16H8V11H9.2V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.4,8.7 10.4,10V11H13.6V10C13.6,8.7 12.8,8.2 12,8.2Z" />
        </svg>
        <span>安全支付由 Stripe 提供</span>
      </div>
    </form>
  );
}

interface PaymentElementWrapperProps {
  selectedPackage: RechargePackage;
  onSuccess: () => void;
  onError: (error: string) => void;
  onCancel: () => void;
}

// 主支付组件包装器
export function PaymentElementWrapper({
  selectedPackage,
  onSuccess,
  onError,
  onCancel,
}: PaymentElementWrapperProps) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showPayment, setShowPayment] = useState(false);
  const [addressInfo, setAddressInfo] = useState<AddressInfo | null>(null);
  const [isAddressValid, setIsAddressValid] = useState(false);

  // 检测用户位置
  useEffect(() => {
    const initializeLocation = async () => {
      try {
        const detectedAddress = await detectUserLocation();
        if (detectedAddress) {
          setAddressInfo(detectedAddress);
          setIsAddressValid(validateAddress(detectedAddress));
        }
      } catch (error) {
        console.warn("无法检测用户位置:", error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeLocation();
  }, []);

  // 创建 Payment Intent
  const createPaymentIntent = async (address: AddressInfo) => {
    try {
      setIsLoading(true);
      const data = await StripeApi.createPaymentIntent({
        productName: selectedPackage.title,
        amount: selectedPackage.price * 100, // 转换为分
        quantity: 1,
        coinAmount: selectedPackage.coins,
        country: address.country,
        postalCode: address.postalCode,
      });

      setClientSecret(data.clientSecret);
      setShowPayment(true);
    } catch (error: any) {
      onError(error.message || "创建支付失败");
    } finally {
      setIsLoading(false);
    }
  };

  // 处理地址变更
  const handleAddressChange = (
    address: AddressInfo | null,
    isValid: boolean,
  ) => {
    setAddressInfo(address);
    setIsAddressValid(isValid);
  };

  // 继续到支付页面
  const handleContinueToPayment = () => {
    if (addressInfo && isAddressValid) {
      createPaymentIntent(addressInfo);
    }
  };

  if (isLoading) {
    return (
      <div className={styles["loading-container"]}>
        <div className={styles["loading-spinner"]}></div>
        <div className={styles["loading-text"]}>正在初始化...</div>
      </div>
    );
  }

  // 显示地址收集界面
  if (!showPayment) {
    // 为地址收集创建临时的Elements上下文
    const addressOptions = {
      mode: "setup" as const,
      currency: "usd",
      appearance: {
        theme: "stripe" as const,
        variables: {
          colorPrimary: "#0570de",
          colorBackground: "#ffffff",
          colorText: "#30313d",
          colorDanger: "#df1b41",
          fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
          spacingUnit: "4px",
          borderRadius: "8px",
        },
      },
    };

    return (
      <Elements options={addressOptions} stripe={stripePromise}>
        <div className={styles["address-container"]}>
          {/* 订单摘要 */}
          <div className={styles["order-summary"]}>
            <h3 className={styles["order-title"]}>订单摘要</h3>
            <div className={styles["package-info"]}>
              <div className={styles["package-name"]}>
                {selectedPackage.title}
              </div>
              <div className={styles["package-details"]}>
                {selectedPackage.coins} 创想值 - ${selectedPackage.price}
              </div>
            </div>
          </div>

          {/* 地址收集 */}
          <AddressCollection
            onAddressChange={handleAddressChange}
            initialAddress={addressInfo || undefined}
          />

          {/* 操作按钮 */}
          <div className={styles["address-actions"]}>
            <button className={styles["cancel-button"]} onClick={onCancel}>
              取消
            </button>
            <button
              className={`${styles["continue-button"]} ${
                !isAddressValid ? styles["disabled"] : ""
              }`}
              onClick={handleContinueToPayment}
              disabled={!isAddressValid}
            >
              继续支付
            </button>
          </div>
        </div>
      </Elements>
    );
  }

  if (!clientSecret) {
    return (
      <div className={styles["error-container"]}>
        <div className={styles["error-text"]}>支付初始化失败</div>
        <button
          className={styles["retry-button"]}
          onClick={() => setShowPayment(false)}
        >
          重试
        </button>
      </div>
    );
  }

  const options = {
    clientSecret,
    appearance: {
      theme: "stripe" as const,
      variables: {
        colorPrimary: "#0570de",
        colorBackground: "#ffffff",
        colorText: "#30313d",
        colorDanger: "#df1b41",
        fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
        spacingUnit: "4px",
        borderRadius: "8px",
      },
    },
  };

  return (
    <Elements options={options} stripe={stripePromise}>
      <PaymentForm
        clientSecret={clientSecret}
        selectedPackage={selectedPackage}
        addressInfo={addressInfo!}
        onSuccess={onSuccess}
        onError={onError}
        onCancel={onCancel}
      />
    </Elements>
  );
}
