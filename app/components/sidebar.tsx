import React, { Fragment, useEffect, useMemo, useRef, useState } from "react";

import styles from "./home.module.scss";
import { About<PERSON><PERSON>bao } from "./doubao-home";

import { IconButton } from "./button";
import { UserAvatar } from "./user-avatar";
import SettingsIcon from "../icons/settings.svg";
import GithubIcon from "../icons/github.svg";
import MenuIcon from "../icons/menu.svg";
import AddIcon from "../icons/add.svg";
import DeleteIcon from "../icons/delete.svg";
import MaskIcon from "../icons/mask.svg";
import McpIcon from "../icons/mcp.svg";
import DragIcon from "../icons/drag.svg";
import SEOIcon from "../icons/settings.svg";
import Locale from "../locales";

import { useAppConfig, useChatStore } from "../store";

import {
  DEFAULT_SIDEBAR_WIDTH,
  MAX_SIDEBAR_WIDTH,
  MIN_SIDEBAR_WIDTH,
  NARROW_SIDEBAR_WIDTH,
  Path,
  REPO_URL,
} from "../constant";

import { Link, useNavigate } from "react-router-dom";
import { isIOS, useMobileScreen } from "../utils";
import dynamic from "next/dynamic";
import { showConfirm } from "./ui-lib";
import clsx from "clsx";
import { isMcpEnabled } from "../mcp/actions";
import { isAuthenticated } from "../utils/auth-token";

const ChatList = dynamic(async () => (await import("./chat-list")).ChatList, {
  loading: () => null,
});

export function useHotKey() {
  const chatStore = useChatStore();

  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      if (e.altKey || e.ctrlKey) {
        if (e.key === "ArrowUp") {
          chatStore.nextSession(-1);
        } else if (e.key === "ArrowDown") {
          chatStore.nextSession(1);
        }
      }
    };

    window.addEventListener("keydown", onKeyDown);
    return () => window.removeEventListener("keydown", onKeyDown);
  });
}

// 简化的侧边栏hook，移除缩放功能
export function useSideBar() {
  const isMobileScreen = useMobileScreen();

  useEffect(() => {
    const sideBarWidth = isMobileScreen ? "100vw" : "280px";
    document.documentElement.style.setProperty("--sidebar-width", sideBarWidth);
  }, [isMobileScreen]);

  return {};
}

export function SideBarContainer(props: {
  children: React.ReactNode;
  className?: string;
}) {
  const isMobileScreen = useMobileScreen();
  const isIOSMobile = useMemo(
    () => isIOS() && isMobileScreen,
    [isMobileScreen],
  );
  const { children, className } = props;
  return (
    <div
      className={clsx(styles.sidebar, className)}
      style={{
        // #3016 disable transition on ios mobile screen
        transition: isMobileScreen && isIOSMobile ? "none" : undefined,
      }}
    >
      {children}
    </div>
  );
}

export function SideBarHeader(props: {
  title?: string | React.ReactNode;
  subTitle?: string | React.ReactNode;
  logo?: React.ReactNode;
  children?: React.ReactNode;
}) {
  const { title, subTitle, logo, children } = props;
  return (
    <Fragment>
      <div className={styles["sidebar-header"]} data-tauri-drag-region>
        <div className={styles["sidebar-title-container"]}>
          <div className={styles["sidebar-title"]} data-tauri-drag-region>
            {title}
          </div>
          <div className={styles["sidebar-sub-title"]}>{subTitle}</div>
        </div>
        <div className={clsx(styles["sidebar-logo"], "no-dark")}>{logo}</div>
      </div>
      {children}
    </Fragment>
  );
}

export function SideBarBody(props: {
  children: React.ReactNode;
  onClick?: (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
}) {
  const { onClick, children } = props;
  return (
    <div className={styles["sidebar-body"]} onClick={onClick}>
      {children}
    </div>
  );
}

export function SideBarTail(props: {
  primaryAction?: React.ReactNode;
  secondaryAction?: React.ReactNode;
}) {
  const { primaryAction, secondaryAction } = props;

  return (
    <div className={styles["sidebar-tail"]}>
      <div className={styles["sidebar-actions"]}>{primaryAction}</div>
      <div className={styles["sidebar-actions"]}>{secondaryAction}</div>
    </div>
  );
}

export function SideBar(props: {
  className?: string;
  onToggleSidebar?: () => void;
}) {
  useHotKey();
  useSideBar();
  const navigate = useNavigate();
  const config = useAppConfig();
  const chatStore = useChatStore();
  const [mcpEnabled, setMcpEnabled] = useState(false);

  useEffect(() => {
    // 检查 MCP 是否启用
    const checkMcpStatus = async () => {
      const enabled = await isMcpEnabled();
      setMcpEnabled(enabled);
      console.log("[SideBar] MCP enabled:", enabled);
    };
    checkMcpStatus();
  }, []);

  return (
    <SideBarContainer {...props}>
      <SideBarHeader
        title={
          <div
            onClick={() => navigate(Path.Home)}
            style={{ cursor: "pointer" }}
          >
            创想AI
          </div>
        }
        subTitle="开启你的创意世界"
        logo={
          <IconButton
            icon={<MenuIcon />}
            onClick={props.onToggleSidebar}
            className={styles["sidebar-toggle-button"]}
          />
        }
      >
        {/* 新对话按钮 - 简化样式，放在标题下方 */}
        <div className={styles["simple-new-chat"]}>
          <IconButton
            icon={<AddIcon />}
            text={Locale.Home.NewChat}
            onClick={() => {
              // 检查用户是否已登录
              if (!isAuthenticated()) {
                navigate(Path.Login);
                return;
              }

              if (config.dontShowMaskSplashScreen) {
                chatStore.newSession();
                navigate(Path.Chat);
              } else {
                navigate(Path.NewChat);
              }
            }}
            className={styles["simple-new-chat-button"]}
          />
        </div>
        <div className={styles["sidebar-header-bar"]}>
          {/* <IconButton
            icon={<MaskIcon />}
            text={Locale.Mask.Name}
            className={styles["sidebar-bar-button"]}
            onClick={() => {
              if (config.dontShowMaskSplashScreen !== true) {
                navigate(Path.NewChat, { state: { fromHome: true } });
              } else {
                navigate(Path.Masks, { state: { fromHome: true } });
              }
            }}
            shadow
          /> */}
          {mcpEnabled && (
            <IconButton
              icon={<McpIcon />}
              text={Locale.Mcp.Name}
              className={styles["sidebar-bar-button"]}
              onClick={() => {
                // 检查用户是否已登录
                if (!isAuthenticated()) {
                  navigate(Path.Login);
                  return;
                }
                navigate(Path.McpMarket, { state: { fromHome: true } });
              }}
              shadow
            />
          )}
        </div>
      </SideBarHeader>
      <SideBarBody
        onClick={(e) => {
          if (e.target === e.currentTarget) {
            navigate(Path.Home);
          }
        }}
      >
        {chatStore.sessions.length > 0 && (
          <div className={styles["chat-history-header"]}>
            <h3 className={styles["chat-history-title"]}>历史记录</h3>
          </div>
        )}
        <ChatList narrow={false} />
      </SideBarBody>
      <SideBarTail
        primaryAction={
          <>
            <div className={clsx(styles["sidebar-action"], styles.mobile)}>
              <IconButton
                icon={<DeleteIcon />}
                onClick={async () => {
                  // 检查用户是否已登录
                  if (!isAuthenticated()) {
                    navigate(Path.Login);
                    return;
                  }

                  if (await showConfirm(Locale.Home.DeleteChat)) {
                    chatStore.deleteSession(chatStore.currentSessionIndex);
                  }
                }}
              />
            </div>
            {/* 关于豆包 */}
            <div className={styles["sidebar-action"]}>
              <AboutDoubao />
            </div>
          </>
        }
        secondaryAction={null}
      />
    </SideBarContainer>
  );
}
