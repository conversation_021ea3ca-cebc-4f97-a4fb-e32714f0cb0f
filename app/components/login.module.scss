@import "../styles/animation.scss";

.login-page {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.login-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 48px;
  width: 100%;
  max-width: 420px;
  min-height: auto;
  position: relative;
  z-index: 1;
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;

  @media (max-width: 768px) {
    padding: 32px 24px;
    margin: 20px;
    border-radius: 12px;
    max-width: none;
  }

  @media (max-width: 480px) {
    padding: 24px 20px;
    margin: 16px;
    border-radius: 12px;
  }
}

.login-header {
  margin-bottom: 32px;

  @media (max-width: 768px) {
    margin-bottom: 24px;
  }
}

.back-button {
  color: var(--black) !important;
  background: rgba(0, 0, 0, 0.05) !important;
  border: none !important;
  padding: 8px 16px !important;
  border-radius: 12px !important;
  font-size: 14px !important;
  transition: all 0.2s ease !important;

  &:hover {
    background: rgba(0, 0, 0, 0.1) !important;
    transform: translateX(-2px);
  }
}

.login-brand {
  text-align: center;
  margin-bottom: 36px;
  flex-shrink: 0;

  @media (max-width: 768px) {
    margin-bottom: 28px;
  }
}

.login-logo {
  width: 64px;
  height: 64px;
  margin: 0 auto 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary);
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(29, 147, 171, 0.2);
  position: relative;
  overflow: hidden;

  svg {
    width: 32px;
    height: 32px;
    color: white;
    z-index: 1;
    position: relative;
  }

  @media (max-width: 768px) {
    width: 56px;
    height: 56px;
    border-radius: 14px;
    margin-bottom: 20px;

    svg {
      width: 28px;
      height: 28px;
    }
  }
}

.login-title {
  font-size: 28px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;

  @media (max-width: 768px) {
    font-size: 24px;
    letter-spacing: -0.3px;
  }
}

.login-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
  font-weight: 400;

  @media (max-width: 768px) {
    font-size: 14px;
  }
}

.login-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .error-message {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    color: #dc2626;
    padding: 10px 14px;
    border-radius: 6px;
    font-size: 13px;
    margin-bottom: 14px;
    border: 1px solid #fecaca;
    animation: shake 0.4s ease-in-out;
    font-weight: 500;
    text-align: center;
    box-shadow: 0 1px 3px rgba(220, 38, 38, 0.1);
    position: relative;
    
    &::before {
      content: "⚠️";
      margin-right: 6px;
      font-size: 12px;
    }
  }

  .form-group {
    margin-bottom: 16px;
    width: 100%;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;

    @media (max-width: 768px) {
      margin-bottom: 20px;
    }
  }

  .form-label {
    font-weight: 600;
    color: var(--black);
    font-size: 14px;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
    display: block;
  }

  .input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    transition: all 0.2s ease;
    width: 100%;
    min-height: 48px;

    &:hover {
      border-color: #cbd5e1;
      background: #f1f5f9;
    }

    &:focus-within {
      border-color: var(--primary);
      background: white;
      box-shadow: 0 0 0 3px rgba(29, 147, 171, 0.1);
    }
  }

  .input-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
    font-size: 16px;
    color: #6b7280;
    flex-shrink: 0;
  }

  .form-input {
    flex: 1;
    padding: 10px 8px 10px 0;
    border: none;
    background: transparent;
    font-size: 14px;
    color: var(--black);
    font-family: inherit;
    min-height: 40px;
    box-sizing: border-box;
    text-align: left;

    &::placeholder {
      color: #9ca3af;
      font-size: 13px;
      font-weight: 400;
      text-align: left;
    }

    &:focus {
      outline: none;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }

    @media (max-width: 768px) {
      padding: 8px 6px 8px 0;
      font-size: 14px;
      min-height: 36px;
      
      &::placeholder {
        font-size: 12px;
      }
    }
  }

  .password-input-wrapper {
    position: relative;
    flex: 1;

    .form-input {
      padding-right: 48px;
    }

    .password-toggle {
      position: absolute;
      right: 14px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      cursor: pointer;
      padding: 6px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      font-size: 16px;
      line-height: 1;

      &:hover {
        background: rgba(0, 0, 0, 0.05);
        transform: translateY(-50%) scale(1.1);
      }

      &:active {
        transform: translateY(-50%) scale(0.95);
      }

      &:disabled {
        cursor: not-allowed;
        opacity: 0.5;
      }
    }
  }

  .form-actions {
    margin: 24px 0 16px 0;

    @media (max-width: 768px) {
      margin: 20px 0 12px 0;
    }
  }

  .login-button {
    width: 100%;
    margin: 0 auto;
    padding: 14px 20px;
    background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
    color: white !important;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    min-height: 48px;
    font-family: inherit;
    display: block;
    box-shadow: 0 2px 8px rgba(29, 147, 171, 0.2);

    &:hover {
      background: linear-gradient(135deg, #1a8299 0%, var(--primary) 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 16px rgba(29, 147, 171, 0.3);
    }

    &:active {
      transform: translateY(0);
      background: linear-gradient(135deg, #156b7a 0%, #1a8299 100%);
    }

    &:disabled {
      background: #9ca3af !important;
      color: white !important;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
      opacity: 0.8;
    }

    &.loading {
      background: #9ca3af !important;
      color: white !important;
      cursor: not-allowed;
      
      &::after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 16px;
        height: 16px;
        margin: -8px 0 0 -8px;
        border: 2px solid transparent;
        border-top: 2px solid #ffffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }

    @media (max-width: 768px) {
      padding: 14px 20px;
      font-size: 15px;
      min-height: 50px;
    }
  }

  .form-footer {
    text-align: center;
    
    .register-link {
      font-size: 14px;
      color: var(--black-light);
      margin: 0;
      line-height: 1.5;

      .link-button {
        background: none;
        border: none;
        color: var(--primary);
        font-weight: 600;
        cursor: pointer;
        text-decoration: none;
        margin-left: 4px;
        transition: all 0.2s ease;
        padding: 2px 4px;
        border-radius: 4px;

        &:hover {
          background: rgba(29, 147, 171, 0.1);
          color: #1a8299;
        }

        &:disabled {
          cursor: not-allowed;
          opacity: 0.5;
        }
      }
    }
  }
}

// Dark mode support
.dark {
  .login-page {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--black) 100%);
  }

  .login-container {
    background: var(--gray-dark);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  }

  .login-title {
    color: var(--white);
  }

  .login-subtitle {
    color: var(--white-light);
  }

  .form-label {
    color: var(--white);
  }

  .form-input {
    background: var(--black);
    color: var(--white);
    border-color: var(--border-color-dark);

    &::placeholder {
      color: var(--white-light);
    }

    &:focus {
      border-color: var(--primary);
      box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.2);
    }

    &:disabled {
      background: var(--gray);
    }
  }

  .password-toggle {
    color: var(--white-light);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
      color: var(--white);
    }
  }

  .back-button {
    color: var(--white) !important;
    background: rgba(255, 255, 255, 0.1) !important;

    &:hover {
      background: rgba(255, 255, 255, 0.2) !important;
    }
  }

  .register-link {
    color: var(--white-light);
  }
}

// Animations
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}