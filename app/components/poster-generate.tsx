import React, { useState, useRef, useCallback } from "react";
import { IconButton } from "./button";
import { useNavigate } from "react-router-dom";
import { Path } from "../constant";
import styles from "./image-edit.module.scss";

import UploadIcon from "../icons/upload.svg";
import BackIcon from "../icons/back.svg";
import CloseIcon from "../icons/close.svg";

// 导入API和工具函数
import {
  uploadImageMultipart,
  kontextPoster,
  ImageType as ApiImageType,
  ImageAspectRatio,
  KontextPosterRequest,
  UploadImageResponse,
  getAspectRatioText,
} from "../api/custom-api/ai-tool-api";
import {
  compressImage,
  IMAGE_COMPRESSION_PRESETS,
} from "../utils/image-compression-lib";


// 类型定义
type TabType = "immediate" | "result";

interface Material {
  id: number;
  image: string;
}

// 常量定义
const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];


// 导入配置文件
import {
  IMAGE_EDIT_EXAMPLES,
  MATERIAL_IMAGES,
} from "../config/images";

// 将配置文件中的素材转换为组件需要的格式
const MATERIALS: Material[] = MATERIAL_IMAGES.map((material) => ({
  id: material.id,
  image: material.path,
}));

export function PosterGenerate() {
  const navigate = useNavigate();

  // 主要状态
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [description, setDescription] = useState<string>("");
  const [aspectRatio, setAspectRatio] = useState<ImageAspectRatio>(ImageAspectRatio.SQUARE);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<TabType>("immediate");
  const [error, setError] = useState<string | null>(null);

  // 任务处理相关状态
  const [taskId, setTaskId] = useState<string | null>(null);
  const [taskStatus, setTaskStatus] = useState<
    "waiting" | "processing" | "completed" | "failed" | null
  >(null);
  const [originalImageFile, setOriginalImageFile] = useState<File | null>(null);

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 工具函数
  const validateFile = useCallback((file: File): string | null => {
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return `不支持的文件类型。请选择 ${ALLOWED_FILE_TYPES.join(", ")} 格式的图片。`;
    }
    if (file.size > MAX_FILE_SIZE) {
      return `文件大小超过限制。请选择小于 ${MAX_FILE_SIZE / 1024 / 1024}MB 的图片。`;
    }
    return null;
  }, []);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  const showError = useCallback((message: string) => {
    setError(message);
    setTimeout(() => setError(null), 5000);
  }, []);

  // 将base64转换为File对象
  const base64ToFile = useCallback((base64: string, filename: string): File => {
    try {
      if (!base64 || typeof base64 !== 'string') {
        throw new Error('Invalid base64 string: empty or not a string');
      }

      if (!base64.startsWith('data:')) {
        throw new Error('Invalid base64 string: not a data URL');
      }

      const arr = base64.split(",");
      if (arr.length !== 2) {
        throw new Error('Invalid base64 string: incorrect format');
      }

      const mime = arr[0].match(/:(.*?);/)?.[1] || "image/png";
      const base64Data = arr[1];

      if (!base64Data) {
        throw new Error('Invalid base64 string: no data part');
      }

      const bstr = atob(base64Data);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], filename, { type: mime });
    } catch (error) {
      console.error('base64ToFile error:', error);
      throw new Error(`Failed to convert base64 to file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, []);

  // 准备文件的辅助函数
  const prepareFiles = useCallback(async () => {
    let originalFile: File;

    // 海报生成只需要一张图片
    if (originalImageFile) {
      originalFile = originalImageFile;
    } else {
      originalFile = base64ToFile(uploadedImage!, "original.png");
    }

    return { originalFile };
  }, [
    originalImageFile,
    uploadedImage,
    base64ToFile,
  ]);

  // 压缩和上传图片的辅助函数
  const compressAndUploadImages = useCallback(async (originalFile: File) => {
    const compressedOriginal = await compressImage(originalFile, IMAGE_COMPRESSION_PRESETS.STANDARD);

    const uploadResult = await uploadImageMultipart({
      uploadImage: compressedOriginal.file,
      imageType: ApiImageType.ORIGINAL,
      imageFormat: compressedOriginal.file.type.split("/")[1] || "png",
    });

    return uploadResult;
  }, []);

  // 提交海报生成任务
  const submitPosterTask = useCallback(async (uploadResult: UploadImageResponse, prompt: string) => {
    if (!uploadResult.name) {
      throw new Error("图片上传失败，未获取到图片名称");
    }

    const posterRequest: KontextPosterRequest = {
      textPrompt: prompt,
      mainImageName: uploadResult.name, // 修复：使用name而不是imageName
      imageAspectRatio: aspectRatio,
    };

    console.log("posterRequest:", posterRequest);
    return await kontextPoster(posterRequest);
  }, [aspectRatio]);

  const handleImageUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // 验证文件
      const validationError = validateFile(file);
      if (validationError) {
        showError(validationError);
        return;
      }

      resetError();

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const imageUrl = e.target?.result as string;
          if (!imageUrl) {
            throw new Error("无法读取图片文件");
          }

          setUploadedImage(imageUrl);
          setOriginalImageFile(file); // 保存原始文件

          // 重置任务状态
          setTaskId(null);
          setTaskStatus(null);
        } catch (err) {
          showError("图片加载失败，请重试");
          console.error("Image upload error:", err);
        }
      };

      reader.onerror = () => {
        showError("文件读取失败，请重试");
      };

      reader.readAsDataURL(file);
    },
    [validateFile, showError, resetError],
  );

  // 删除图片
  const removeImage = useCallback(() => {
    try {
      resetError();
      setUploadedImage(null);
      setOriginalImageFile(null);

      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }

      // 重置任务状态
      setTaskId(null);
      setTaskStatus(null);
    } catch (err) {
      console.error("Remove image error:", err);
      showError("删除图片时出错");
    }
  }, [resetError, showError]);

  const handleProcess = useCallback(async () => {
    if (!uploadedImage) {
      showError("请先上传图片");
      return;
    }

    // 描述信息不是必填的，如果为空则使用默认描述
    const finalDescription = description.trim() || "海报生成";

    // 跳转到生成结果tab页
    setActiveTab("result");
    setIsProcessing(true);
    setTaskStatus("processing");
    resetError();

    try {
      // 准备文件
      const { originalFile } = await prepareFiles();

      // 压缩和上传图片
      const uploadResult = await compressAndUploadImages(originalFile);

      // 提交海报生成任务
      const posterResult = await submitPosterTask(uploadResult, finalDescription);

      // 更新状态
      setTaskId(posterResult.prompt_id);
      setTaskStatus("processing");
    } catch (error) {
      console.error("处理失败:", error);
      // 处理失败时清除任务状态，回到初始模式
      setTaskStatus(null);
      setTaskId(null);
      showError(
        error instanceof Error ? error.message : "处理失败，请稍后重试",
      );
      setActiveTab("immediate");
    } finally {
      setIsProcessing(false);
    }
  }, [
    uploadedImage,
    description,
    showError,
    resetError,
    prepareFiles,
    compressAndUploadImages,
    submitPosterTask,
  ]);

  const canProcess = uploadedImage && !isProcessing;

  return (
    <React.Fragment>
      <div className={styles.container}>
        {/* 头部导航 */}
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <IconButton
              icon={<BackIcon />}
              text="返回"
              onClick={() => navigate(-1)}
              className={styles.backButton}
            />
            <h1 className={styles.title}>海报生成</h1>
          </div>
          <div className={styles.headerRight}>
            <button
              className={styles.myWorksButton}
              onClick={() => navigate(Path.MyWorks)}
            >
              我的作品
            </button>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className={styles.errorMessage}>
            <span>{error}</span>
            <button onClick={resetError} className={styles.errorClose}>
              ×
            </button>
          </div>
        )}

        {/* 主要内容区域 */}
        <div className={styles.mainContainer}>
        {/* 左侧案例展示区 - 占更大宽度 */}
        <div className={styles.exampleArea}>
          {/* 案例展示 */}
          <div className={styles.exampleSection}>
            <h3>案例</h3>
            <div className={styles.exampleContainer}>
              {IMAGE_EDIT_EXAMPLES.map((example) => (
                <div key={example.id} className={styles.exampleItem}>
                  <div className={styles.exampleImages}>
                    <div className={styles.beforeAfter}>
                      <div className={styles.imageContainer}>
                        <img src={example.beforeImage} alt="处理前" />
                        <span className={styles.imageLabel}>原图</span>
                      </div>
                      <div className={styles.imageContainer}>
                        <img src={example.afterImage} alt="处理后" />
                        <span className={styles.imageLabel}>海报</span>
                      </div>
                      <div className={styles.arrow}>→</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 素材展示 */}
          <div className={styles.materialSection}>
            <h3>推荐素材</h3>
            <div className={styles.materialGrid}>
              {MATERIALS.map((material) => (
                <div
                  key={material.id}
                  className={styles.materialItem}
                  onClick={async () => {
                    try {
                      setUploadedImage(material.image);

                      // 将素材图片转换为File对象
                      const response = await fetch(material.image);
                      const blob = await response.blob();
                      const file = new File(
                        [blob],
                        `material-${material.id}.png`,
                        { type: "image/png" },
                      );
                      setOriginalImageFile(file);

                      // 重置任务状态
                      setTaskId(null);
                      setTaskStatus(null);
                    } catch (error) {
                      console.error("素材加载失败:", error);
                      showError("素材加载失败，请重试");
                    }
                  }}
                >
                  <div className={styles.materialImageWrapper}>
                    <img src={material.image} alt="素材" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 右侧操作区 - 相对较小 */}
        <div className={styles.operationArea}>
          <div className={styles.editSection}>
            {/* 统一的内容容器 */}
            <div className={styles.contentContainer}>
              {/* Tab导航 */}
              <div className={styles.tabNavigation}>
                <button
                  className={`${styles.tab} ${
                    activeTab === "immediate" ? styles.activeTab : ""
                  }`}
                  onClick={() => setActiveTab("immediate")}
                >
                  立即使用
                </button>
                <button
                  className={`${styles.tab} ${
                    activeTab === "result" ? styles.activeTab : ""
                  }`}
                  onClick={() => setActiveTab("result")}
                >
                  生成结果
                </button>
              </div>

              {/* Tab内容 */}
              <div className={styles.tabContent}>
                {activeTab === "immediate" && (
                  <div className={styles.immediateTab}>
                    {/* 图片上传区域 */}
                    <div className={styles.uploadSection}>
                      <label className={styles.uploadLabel}>
                        上传图片<span className={styles.required}>*</span>
                      </label>
                      <div
                        className={styles.uploadArea}
                        onClick={
                          uploadedImage
                            ? undefined
                            : () => fileInputRef.current?.click()
                        }
                      >
                        {uploadedImage ? (
                          <div className={styles.imagePreview}>
                            <img src={uploadedImage} alt="上传的图片" />
                            <div className={styles.imageControls}>
                              <div></div>
                              <button
                                className={`${styles.controlButton} ${styles.deleteButton}`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  removeImage();
                                }}
                                title="删除图片"
                              >
                                <CloseIcon />
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div className={styles.uploadPlaceholder}>
                            <div className={styles.uploadIcon}>
                              <UploadIcon />
                            </div>
                            <div className={styles.uploadText}>
                              拖拽文件到这里或点击上传
                            </div>
                          </div>
                        )}
                      </div>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        style={{ display: "none" }}
                      />
                    </div>

                    {/* 比例设置 */}
                    <div className={styles.uploadSection}>
                      <label className={styles.uploadLabel}>
                        图片比例<span className={styles.required}>*</span>
                      </label>
                      <div className={styles.aspectRatioOptions}>
                        {Object.values(ImageAspectRatio)
                          .filter((value) => typeof value === 'number')
                          .map((ratio) => (
                            <button
                              key={ratio}
                              className={`${styles.aspectRatioButton} ${
                                aspectRatio === ratio ? styles.active : ""
                              }`}
                              onClick={() => setAspectRatio(ratio as ImageAspectRatio)}
                            >
                              {getAspectRatioText(ratio as ImageAspectRatio)}
                            </button>
                          ))}
                      </div>
                    </div>

                    {/* 描述输入区域 */}
                    <div className={styles.uploadSection}>
                      <label className={styles.uploadLabel}>
                        描述信息（可选）
                        <span
                          className={`${styles.charCount} ${
                            description.length > 450
                              ? styles.error
                              : description.length > 400
                              ? styles.warning
                              : ""
                          }`}
                        >
                          {description.length}/500
                        </span>
                      </label>
                      <textarea
                        className={styles.descriptionInput}
                        value={description}
                        onChange={(e) =>
                          setDescription(e.target.value.slice(0, 500))
                        }
                        placeholder="可选：描述您希望生成的海报风格，例如：现代简约、商务风格、创意设计等..."
                        rows={3}
                        maxLength={500}
                      />
                    </div>

                    {/* 处理按钮 */}
                    <button
                      className={`${styles.processButton} ${
                        canProcess ? styles.enabled : styles.disabled
                      }`}
                      onClick={handleProcess}
                      disabled={!canProcess}
                    >
                      {isProcessing ? "处理中..." : "Go"}
                    </button>

                    {/* 底部信息 */}
                    <div className={styles.bottomInfo}>
                      <div className={styles.credits}>
                        <span className={styles.creditsCost}>
                          消耗50创想值
                        </span>
                        <span className={styles.creditsDivider}>•</span>
                        <span
                          className={styles.creditsLink}
                          onClick={() => navigate(Path.Recharge)}
                        >
                          获取更多创想值
                        </span>
                      </div>
                    </div>
                  </div>
                )}

              {activeTab === "result" && (
                <div>
            {/* 处理中状态 */}
            {isProcessing && !taskId && (
              <div className={styles.statusContainer}>
                <div className={styles.processingCard}>
                  <div className={styles.statusRow}>
                    <div className={styles.statusLeft}>
                      <div className={styles.spinner}></div>
                      <span className={styles.statusLabel}>处理中</span>
                    </div>
                    <span className={styles.timeLabel}>
                      {new Date().toLocaleTimeString("zh-CN", {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </span>
                  </div>
                  {description.trim() && (
                    <div className={styles.promptText}>
                      &ldquo;{description}&rdquo;
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* 任务已提交状态 */}
            {taskId && (
              <div className={styles.statusContainer}>
                <div className={styles.successCard}>
                  <div className={styles.successRow}>
                    <div className={styles.checkIcon}>✓</div>
                    <div className={styles.successText}>
                      <div className={styles.successTitle}>任务已提交</div>
                      <div className={styles.successDesc}>
                        可前往&ldquo;我的作品&rdquo;页面查看结果
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 空状态 */}
            {!isProcessing && !taskStatus && !result && (
              <div className={styles.emptyResult}>
                <div className={styles.emptyIcon}>🎨</div>
                <h3>暂无任务</h3>
                <p>点击&ldquo;Go&rdquo;按钮开始生成海报</p>
              </div>
            )}

            {/* 生成结果显示 */}
            {result && !isProcessing && !taskStatus && (
              <div className={styles.resultContent}>
                <div className={styles.resultHeader}>
                  <div className={styles.resultMeta}>
                    <span className={styles.resultDate}>
                      {new Date().toLocaleDateString("zh-CN", {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </span>
                  </div>
                  <div className={styles.resultActions}>
                    <button
                      className={styles.actionButton}
                      title="下载"
                      onClick={() => {
                        const link = document.createElement("a");
                        link.href = result!;
                        link.download = "poster-result.png";
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                      }}
                    >
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                        <polyline points="7,10 12,15 17,10" />
                        <line x1="12" y1="15" x2="12" y2="3" />
                      </svg>
                    </button>
                  </div>
                </div>
                <div className={styles.resultStatus}>
                  <span className={styles.statusText}>生成成功</span>
                  <span className={styles.statusBadge}>完成</span>
                </div>
                <div className={styles.resultImageWrapper}>
                  <img src={result} alt="生成结果" className={styles.resultImage} />
                </div>
              </div>
            )}
                </div>
              )}
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </React.Fragment>
  );
}
