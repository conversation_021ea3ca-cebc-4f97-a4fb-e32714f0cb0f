@import "../styles/animation.scss";

.payment-form {
  max-width: 520px;
  margin: 2vh auto;
  padding: 2rem;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08), 0 8px 16px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(226, 232, 240, 0.6);
  backdrop-filter: blur(10px);
  animation: slide-in ease 0.3s;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    border-radius: 20px 20px 0 0;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.01) 0%, rgba(118, 75, 162, 0.01) 100%);
    pointer-events: none;
  }
  
  @media (prefers-color-scheme: dark) {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border-color: rgba(71, 85, 105, 0.6);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4), 0 8px 16px rgba(0, 0, 0, 0.2);

    &::before {
      background: linear-gradient(90deg, #93c5fd 0%, #c084fc 50%, #fbbf24 100%);
    }
  }
  

}

.payment-header {
  text-align: center;
  margin-bottom: 2.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;

  @media (prefers-color-scheme: dark) {
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .payment-title {
    font-size: 1.75rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.75rem;
    letter-spacing: -0.5px;
    line-height: 1.2;

    @media (prefers-color-scheme: dark) {
      background: linear-gradient(135deg, #93c5fd 0%, #c084fc 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    @media (max-width: 640px) {
    font-size: 1.5rem;
  }

  @media (max-width: 480px) {
    font-size: 1.375rem;
  }
}

.payment-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 1rem;
  min-height: 0; /* 确保flex子元素能够正确收缩 */
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 2px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    
    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
  }
}

.package-summary {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2.5rem;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
      pointer-events: none;
    }

    @media (prefers-color-scheme: dark) {
      background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
      border-color: rgba(75, 85, 99, 0.8);
    }

    @media (max-width: 640px) {
      padding: 1.5rem;
      border-radius: 12px;
    }

    .package-name {
      font-size: 18px;
      font-weight: 600;
      color: var(--primary);
      margin-bottom: 8px;
    }

    .package-details {
      font-size: 15px;
      color: var(--black-60);
      line-height: 1.5;
    }

    .package-coins {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.package-price {
  font-size: 18px;
  font-weight: 700;
  color: #007bff;
  text-align: center;
  padding: 8px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 8px;
  border: 1px solid #dee2e6;
}
  }
}

.payment-element-container {
  margin-bottom: 28px;
  
  // Stripe Elements 样式覆盖
  :global(.StripeElement) {
    padding: 20px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);

    &:focus {
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(0, 0, 0, 0.05);
      transform: translateY(-1px);
    }
  }

  :global(.StripeElement--invalid) {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
  }

  :global(.StripeElement--complete) {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }
}

// Express Checkout Element 容器样式
.express-checkout-container {
  margin-bottom: 24px;
  padding: 6px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
  border-radius: 16px;
  border: 1px solid rgba(102, 126, 234, 0.1);
  
  // Express Checkout Element 样式覆盖
  :global(.StripeExpressCheckoutElement) {
    border-radius: 12px;
    overflow: hidden;
    min-height: 52px;
  }
  
  // Google Pay 按钮样式优化（优先显示）
  :global(.StripeExpressCheckoutElement button) {
    border-radius: 12px !important;
    height: 52px !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 8px !important;
    width: 100% !important;
    
    &:hover {
      transform: translateY(-2px) scale(1.01) !important;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
    }
    
    &:active {
      transform: translateY(0) scale(0.98) !important;
    }
    
    // Google Pay 按钮特殊样式
    &[data-testid*="google"], &[aria-label*="Google"] {
      background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #fbbc05 75%, #ea4335 100%) !important;
      color: white !important;
      border: none !important;
      font-weight: 700 !important;
      order: -1 !important; // 确保显示在最前面
    }
  }
}

// 支付方式分隔线
.payment-divider {
  display: flex;
  align-items: center;
  margin: 28px 0;
  
  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
  }
  
  span {
    padding: 0 20px;
    font-size: 14px;
    font-weight: 500;
    color: var(--black-60);
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
    border-radius: 20px;
    border: 1px solid rgba(0, 0, 0, 0.05);
  }
}

.error-message {
  color: var(--red);
  font-size: 14px;
  margin-bottom: 16px;
  padding: 12px;
  background: rgba(223, 27, 65, 0.1);
  border: 1px solid rgba(223, 27, 65, 0.2);
  border-radius: 8px;
  text-align: center;
}

.payment-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;

  .cancel-button {
    flex: 1;
    padding: 14px 24px;
    background: var(--gray);
    color: var(--black);
    border: 1px solid var(--border-in-light);
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      background: var(--hover-color);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .pay-button {
    flex: 2;
    padding: 16px 28px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    &:hover:not(:disabled) {
      transform: translateY(-2px) scale(1.02);
      box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
      
      &::before {
        left: 100%;
      }
    }

    &:active:not(:disabled) {
      transform: translateY(0) scale(0.98);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
    }

    .loading-spinner {
      width: 18px;
      height: 18px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

.security-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 13px;
  color: var(--black-60);
  text-align: center;

  .security-icon {
    width: 16px;
    height: 16px;
    color: var(--green);
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-in-light);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }

  .loading-text {
    font-size: 16px;
    color: var(--black-60);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;

  .error-text {
    font-size: 16px;
    color: var(--red);
    margin-bottom: 20px;
  }

  .retry-button {
    padding: 12px 24px;
    background: var(--primary);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--primary-dark);
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .payment-form {
    margin: 1vh 1rem;
    padding: 1.5rem;
    max-height: 90vh;
    border-radius: 16px;
    max-width: calc(100% - 2rem);
  }

  .address-summary {
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .payment-form {
    margin: 0.5vh 0.75rem;
    padding: 1.25rem;
    max-height: 92vh;
    border-radius: 12px;
    max-width: calc(100% - 1.5rem);
  }

  .payment-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    
    .payment-title {
      font-size: 20px;
      margin-bottom: 12px;
    }
    
    .package-summary {
      .package-name {
        font-size: 16px;
      }
      
      .package-details {
        font-size: 14px;
      }
      
      .package-price {
        font-size: 16px;
        padding: 6px 12px;
      }
    }
  }

  .express-checkout-container {
    margin-bottom: 20px;
    padding: 4px;
    
    :global(.StripeExpressCheckoutElement button) {
      height: 48px !important;
      font-size: 15px !important;
    }
  }

  .payment-element-container {
    margin-bottom: 20px;
    
    :global(.StripeElement) {
      padding: 16px;
    }
  }

  .payment-actions {
    flex-direction: column;
    gap: 10px;

    .cancel-button,
    .pay-button {
      flex: none;
      padding: 12px 20px;
    }
    
    .pay-button {
      font-size: 15px;
    }
  }
  
  .security-info {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .payment-form {
    margin: 4px;
    padding: 16px;
    max-height: 98vh;
  }
  
  .payment-header {
    .payment-title {
      font-size: 18px;
    }
  }
  
  .express-checkout-container {
    :global(.StripeExpressCheckoutElement button) {
      height: 44px !important;
      font-size: 14px !important;
    }
  }
  
  .address-container {
    padding: 20px;
  }
  
  .order-summary {
    margin-bottom: 20px;
  }
  
  .address-actions {
    gap: 12px;
  }
  
  .continue-button,
  .cancel-button {
    padding: 12px 20px;
    font-size: 0.875rem;
  }
}

// 地址收集界面样式
.address-container {
  max-width: 520px;
  margin: 0 auto;
  padding: 32px;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.08), 0 8px 16px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  animation: slide-in ease 0.3s;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  }
}

/* 地址摘要样式 */
.address-summary {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border: 1px solid rgba(203, 213, 225, 0.6);
  border-radius: 12px;
  padding: 1.25rem;
  margin-bottom: 1.5rem;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 51, 234, 0.02) 100%);
    pointer-events: none;
  }

  @media (prefers-color-scheme: dark) {
    background: linear-gradient(135deg, #334155 0%, #1e293b 100%);
    border-color: rgba(71, 85, 105, 0.6);
  }

  @media (max-width: 640px) {
    padding: 1rem;
    border-radius: 10px;
  }
}

.address-title {
  font-size: 1rem;
  font-weight: 600;
  color: #475569;
  margin: 0 0 0.875rem 0;
  letter-spacing: -0.025em;
  position: relative;
  z-index: 1;

  @media (prefers-color-scheme: dark) {
    color: #cbd5e1;
  }

  @media (max-width: 640px) {
    font-size: 0.9375rem;
  }
}

.address-info {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  position: relative;
  z-index: 1;
}

.address-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem 0.875rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.5);
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  @media (prefers-color-scheme: dark) {
    background: rgba(30, 41, 59, 0.7);
    border-color: rgba(71, 85, 105, 0.5);

    &:hover {
      background: rgba(30, 41, 59, 0.9);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }
  }
}

.address-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  letter-spacing: -0.025em;

  @media (prefers-color-scheme: dark) {
    color: #94a3b8;
  }

  @media (max-width: 640px) {
    font-size: 0.8125rem;
  }
}

.address-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
  letter-spacing: -0.025em;

  @media (prefers-color-scheme: dark) {
    color: #e2e8f0;
  }

  @media (max-width: 640px) {
    font-size: 0.8125rem;
  }
}

.order-summary {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 32px;
  border: 1px solid rgba(148, 163, 184, 0.1);
}

.order-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 16px 0;
  text-align: center;
}

.package-info {
  text-align: center;
}

.package-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #334155;
  margin-bottom: 8px;
}

.package-details {
  font-size: 1rem;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  &::before {
    content: '💎';
    font-size: 1.125rem;
  }
}

.address-actions {
  display: flex;
  gap: 16px;
  margin-top: 32px;
  justify-content: space-between;
}

.cancel-button {
  flex: 1;
  padding: 16px 24px;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #475569;
  border: 1px solid #cbd5e1;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
  }
}

.continue-button {
  flex: 2;
  padding: 16px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }
  
  &:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    
    &::before {
      left: 100%;
    }
  }
  
  &:active {
    transform: translateY(-1px);
  }
  
  &.disabled {
    background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
    
    &:hover {
      background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
      transform: none;
      box-shadow: none;
      
      &::before {
        left: -100%;
      }
    }
  }
}