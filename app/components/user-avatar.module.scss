.user-avatar-container {
  position: relative;
  display: inline-block;
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  background: transparent;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.avatar-circle {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
  box-shadow: 0 3px 12px rgba(102, 126, 234, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
  }

  &:hover::before {
    animation: shimmer 1.5s ease-in-out;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
    opacity: 0;
  }
}

.user-name {
  font-size: 15px;
  font-weight: 600;
  color: var(--text-color);
  max-width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.3px;
}

.dropdown-arrow {
  font-size: 12px;
  color: var(--text-color-secondary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  
  &.open {
    transform: rotate(180deg);
    color: var(--primary-color);
  }
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: #ffffff;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 16px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15), 0 4px 16px rgba(0, 0, 0, 0.08);
  min-width: 220px;
  z-index: 1000;
  overflow: hidden;
  animation: dropdownFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 8px 0;

  &::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 24px;
    width: 12px;
    height: 12px;
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.08);
    border-bottom: none;
    border-right: none;
    transform: rotate(45deg);
  }
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dropdown-item {
  padding: 16px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 15px;
  font-weight: 400;
  color: #333333;
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 8px;
  border-radius: 8px;

  &:hover {
    background: rgba(0, 0, 0, 0.04);
    color: #333333;
  }

  &:active {
    background: rgba(0, 0, 0, 0.08);
  }

  span {
    display: block;
    position: relative;
    z-index: 1;
    flex: 1;
  }

  .menu-icon {
    width: 18px;
    height: 18px;
    opacity: 0.7;
  }

  .recommended {
    background: #007AFF;
    color: white;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
    margin-left: auto;
  }
}

.dropdown-divider {
  height: 1px;
  background-color: rgba(0, 0, 0, 0.08);
  margin: 8px 16px;
}

// 移动端适配
@media (max-width: 768px) {
  .user-name {
    display: none;
  }
  
  .user-avatar {
    padding: 6px;
  }
  
  .dropdown-menu {
    right: -8px;
  }
}

.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0;
}

.login-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 25px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 100px;
  justify-content: center;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  span {
    font-size: 16px;
  }
}