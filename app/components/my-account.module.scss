.my-account-page {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, rgba(29, 147, 171, 0.02) 0%, var(--white) 100%);
  position: relative;
  overflow: hidden;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  background: var(--white);
  border-bottom: 1px solid rgba(29, 147, 171, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-button {
  flex-shrink: 0;
  transition: transform 0.2s ease;

  &:hover {
    transform: translateX(-2px);
  }
}

.title {
  font-size: 28px;
  font-weight: 700;
  color: var(--black);
  margin: 0;
  background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.close-button {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: var(--white);
  border: 2px solid rgba(29, 147, 171, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 20px;
  color: var(--black-70);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  
  &:hover {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(29, 147, 171, 0.2);
  }
  
  &:active {
    transform: scale(0.95);
  }
}

.content {
  flex: 1;
  display: flex;
  gap: 24px;
  padding: 24px;
  overflow: hidden;
  min-height: 0;
}

.sidebar {
  width: 400px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.user-info-card {
  background: var(--white);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(29, 147, 171, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(29, 147, 171, 0.12);
  }
}

.avatar {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 24px;
  flex-shrink: 0;
  box-shadow: 0 4px 16px rgba(29, 147, 171, 0.2);
}

.user-details {
  flex: 1;
}

.username {
  font-size: 20px;
  font-weight: 600;
  color: var(--black);
  margin: 0 0 6px 0;
  letter-spacing: 0.3px;
}

.org-code {
  font-size: 14px;
  color: var(--black-60);
  margin: 0;
  font-weight: 400;
}

.balance-card {
  background: var(--white);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(29, 147, 171, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(29, 147, 171, 0.12);
  }
}

.balance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--black);
    margin: 0;
  }
}

.refresh-button {
  background: var(--white);
  border: 2px solid rgba(29, 147, 171, 0.15);
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  color: var(--black-70);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  &:hover {
    background: var(--primary);
    border-color: var(--primary);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(29, 147, 171, 0.2);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    
    &:hover {
      background: var(--white);
      border-color: rgba(29, 147, 171, 0.15);
      color: var(--black-70);
      transform: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }
  }
}

.balance-content {
  text-align: center;
  padding: 12px 0;
}

.balance-amount {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.amount {
  font-size: 32px;
  font-weight: 700;
  color: var(--primary);
  background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.unit {
  font-size: 16px;
  font-weight: 500;
  color: var(--black-60);
}

.last-updated {
  font-size: 12px;
  color: var(--text-color-secondary);
  margin: 0;
}

.error-message {
  text-align: center;
  color: var(--red);
  
  p {
    margin: 0 0 8px 0;
    font-size: 14px;
  }
  
  button {
    background: var(--red);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    
    &:hover {
      background: var(--red-dark);
    }
  }
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 12px;
}

.recharge-button {
  background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 14px 20px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  box-shadow: 0 4px 16px rgba(29, 147, 171, 0.2);
  letter-spacing: 0.3px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(29, 147, 171, 0.3);
  }
  
  &:active {
    transform: translateY(0);
  }
}

.history-button {
  background: var(--white);
  color: var(--black-70);
  border: 2px solid rgba(29, 147, 171, 0.15);
  border-radius: 12px;
  padding: 14px 20px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  letter-spacing: 0.3px;

  &:hover {
    background: var(--primary);
    border-color: var(--primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(29, 147, 171, 0.2);
  }
  
  &:active {
    transform: translateY(0);
  }
}

.button-icon {
  width: 16px;
  height: 16px;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  overflow-y: auto;
  padding-right: 0;
  min-height: 0;
  width: 100%;
}

.content-card {
  background: var(--white);
  border-radius: 16px;
  padding: 32px;
  border: 1px solid rgba(29, 147, 171, 0.1);
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  flex: 1;
  // min-height: 250px;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(29, 147, 171, 0.12);
  }

  h2 {
    font-size: 24px;
    font-weight: 700;
    color: var(--black);
    margin: 0 0 28px 0;
    border-bottom: 3px solid rgba(29, 147, 171, 0.1);
    padding-bottom: 16px;
    background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  width: 100%;
}

.overview-item {
  text-align: center;
  padding: 28px;
  border: 1px solid rgba(29, 147, 171, 0.1);
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(29, 147, 171, 0.03);

  &:hover {
    border-color: var(--primary);
    background: var(--white);
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(29, 147, 171, 0.15);
  }

  h4 {
    font-size: 14px;
    color: var(--black-60);
    margin: 0 0 16px 0;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.8px;
  }
}

.overview-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary);
  margin: 0;
  background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 24px;
  border: 1px solid rgba(29, 147, 171, 0.1);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--white);
  width: 100%;
  box-sizing: border-box;

  &:hover {
    border-color: var(--primary);
    background: rgba(29, 147, 171, 0.03);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(29, 147, 171, 0.15);
  }
}

.action-icon {
  width: 52px;
  height: 52px;
  border-radius: 16px;
  background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 22px;
  flex-shrink: 0;
  box-shadow: 0 4px 16px rgba(29, 147, 171, 0.3);
}

.action-text {
  flex: 1;

  h4 {
    font-size: 18px;
    font-weight: 600;
    color: var(--black);
    margin: 0 0 8px 0;
    letter-spacing: 0.3px;
  }

  p {
    font-size: 15px;
    color: var(--black-60);
    margin: 0;
    line-height: 1.5;
  }
}

// 移动端适配
@media (max-width: 768px) {
  .my-account-page {
    height: 100vh;
    overflow: hidden;
  }

  .content {
    flex-direction: column;
    padding: 16px;
    gap: 20px;
    overflow-y: auto;
    height: calc(100vh - 80px);
  }

  .sidebar {
    width: 100%;
    position: static;
    z-index: auto;
    order: 1;
  }

  .main-content {
    margin-top: 0;
    position: static;
    z-index: auto;
    order: 2;
    margin-bottom: 20px;
  }

  .header {
    padding: 12px 16px;
    flex-shrink: 0;
  }

  .title {
    font-size: 18px;
  }

  .user-info-card {
    padding: 16px;
    margin-bottom: 0;
    position: static;
    z-index: auto;
  }

  .balance-card {
    padding: 16px;
    margin-bottom: 0;
    position: static;
    z-index: auto;
  }

  .content-card {
    padding: 16px;
  }

  .avatar {
    width: 48px;
    height: 48px;
    font-size: 18px;
  }

  .username {
    font-size: 16px;
  }

  .amount {
    font-size: 22px;
  }

  .overview-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .action-item {
    padding: 16px;
    gap: 16px;
  }
  
  .action-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .quick-actions {
    margin-top: 0;
    position: static;
    z-index: auto;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 8px 12px;
  }

  .title {
    font-size: 16px;
  }

  .content {
    padding: 12px;
    gap: 16px;
  }

  .user-info-card,
  .balance-card,
  .content-card {
    padding: 12px;
  }

  .action-item {
    padding: 12px;
    gap: 12px;
  }

  .action-icon {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }

  .action-text h4 {
    font-size: 16px;
  }

  .action-text p {
    font-size: 13px;
  }
}