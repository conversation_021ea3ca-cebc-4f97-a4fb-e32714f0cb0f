"use client";

import React, { useState, useEffect } from "react";
import styles from "./address-collection.module.scss";

export interface AddressInfo {
  country: string;
  postalCode: string;
}

interface AddressCollectionProps {
  onAddressChange: (address: AddressInfo | null, isValid: boolean) => void;
  initialAddress?: AddressInfo;
}

// 国家列表
const COUNTRIES = [
  { code: "US", name: "美国" },
  { code: "CN", name: "中国" },
  { code: "GB", name: "英国" },
  { code: "CA", name: "加拿大" },
  { code: "AU", name: "澳大利亚" },
  { code: "DE", name: "德国" },
  { code: "FR", name: "法国" },
  { code: "JP", name: "日本" },
  { code: "KR", name: "韩国" },
  { code: "SG", name: "新加坡" },
  { code: "HK", name: "香港" },
  { code: "TW", name: "台湾" },
  { code: "IT", name: "意大利" },
  { code: "ES", name: "西班牙" },
  { code: "NL", name: "荷兰" },
  { code: "BE", name: "比利时" },
  { code: "CH", name: "瑞士" },
  { code: "AT", name: "奥地利" },
  { code: "SE", name: "瑞典" },
  { code: "NO", name: "挪威" },
  { code: "DK", name: "丹麦" },
  { code: "FI", name: "芬兰" },
  { code: "IE", name: "爱尔兰" },
  { code: "PT", name: "葡萄牙" },
  { code: "BR", name: "巴西" },
  { code: "MX", name: "墨西哥" },
  { code: "IN", name: "印度" },
  { code: "TH", name: "泰国" },
  { code: "MY", name: "马来西亚" },
  { code: "ID", name: "印度尼西亚" },
  { code: "PH", name: "菲律宾" },
  { code: "VN", name: "越南" },
  { code: "RU", name: "俄罗斯" },
  { code: "PL", name: "波兰" },
  { code: "CZ", name: "捷克" },
  { code: "HU", name: "匈牙利" },
  { code: "RO", name: "罗马尼亚" },
  { code: "BG", name: "保加利亚" },
  { code: "HR", name: "克罗地亚" },
  { code: "SI", name: "斯洛文尼亚" },
  { code: "SK", name: "斯洛伐克" },
  { code: "EE", name: "爱沙尼亚" },
  { code: "LV", name: "拉脱维亚" },
  { code: "LT", name: "立陶宛" },
  { code: "GR", name: "希腊" },
  { code: "CY", name: "塞浦路斯" },
  { code: "MT", name: "马耳他" },
  { code: "LU", name: "卢森堡" },
];

export function AddressCollection({
  onAddressChange,
  initialAddress,
}: AddressCollectionProps) {
  const [country, setCountry] = useState(initialAddress?.country || "");
  const [postalCode, setPostalCode] = useState(
    initialAddress?.postalCode || "",
  );
  const [errors, setErrors] = useState<{
    country?: string;
    postalCode?: string;
  }>({});

  // 验证输入
  const validateInputs = () => {
    const newErrors: { country?: string; postalCode?: string } = {};

    if (!country) {
      newErrors.country = "请选择国家";
    }

    if (!postalCode.trim()) {
      newErrors.postalCode = "请输入邮政编码";
    } else if (
      country === "US" &&
      !/^\d{5}(-\d{4})?$/.test(postalCode.trim())
    ) {
      newErrors.postalCode = "美国邮编格式：12345 或 12345-6789";
    } else if (country === "CN" && !/^\d{6}$/.test(postalCode.trim())) {
      newErrors.postalCode = "中国邮编格式：6位数字";
    } else if (
      country === "GB" &&
      !/^[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}$/i.test(postalCode.trim())
    ) {
      newErrors.postalCode = "英国邮编格式：SW1A 1AA";
    } else if (
      country === "CA" &&
      !/^[A-Z]\d[A-Z]\s?\d[A-Z]\d$/i.test(postalCode.trim())
    ) {
      newErrors.postalCode = "加拿大邮编格式：K1A 0A6";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 当输入改变时通知父组件
  useEffect(() => {
    if (country && postalCode) {
      const isValid = validateInputs();
      const addressInfo: AddressInfo = {
        country,
        postalCode: postalCode.trim(),
      };
      onAddressChange(addressInfo, isValid);
    } else {
      onAddressChange(null, false);
    }
  }, [country, postalCode]);

  const handleCountryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setCountry(e.target.value);
    setErrors((prev) => ({ ...prev, country: undefined }));
  };

  const handlePostalCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPostalCode(e.target.value);
    setErrors((prev) => ({ ...prev, postalCode: undefined }));
  };

  const getPostalCodePlaceholder = () => {
    switch (country) {
      case "US":
        return "12345";
      case "CN":
        return "100000";
      case "GB":
        return "SW1A 1AA";
      case "CA":
        return "K1A 0A6";
      case "DE":
        return "10115";
      case "FR":
        return "75001";
      case "JP":
        return "100-0001";
      default:
        return "请输入邮政编码";
    }
  };

  return (
    <div className={styles["address-container"]}>
      <div className={styles["header"]}>
        <h3 className={styles["title"]}>账单地址</h3>
        <p className={styles["subtitle"]}>请提供您的国家和邮编信息</p>
      </div>

      <div className={styles["form-container"]}>
        <div className={styles["custom-form"]}>
          <div className={styles["form-field"]}>
            <label className={styles["form-label"]} htmlFor="country">
              国家或地区
            </label>
            <select
              id="country"
              className={`${styles["form-select"]} ${
                errors.country ? styles["form-error"] : ""
              }`}
              value={country}
              onChange={handleCountryChange}
            >
              <option value="">请选择国家或地区</option>
              {COUNTRIES.map((c) => (
                <option key={c.code} value={c.code}>
                  {c.name}
                </option>
              ))}
            </select>
            {errors.country && (
              <div className={styles["error-text"]}>{errors.country}</div>
            )}
          </div>

          <div className={styles["form-field"]}>
            <label className={styles["form-label"]} htmlFor="postalCode">
              邮政编码
            </label>
            <input
              id="postalCode"
              type="text"
              className={`${styles["form-input"]} ${
                errors.postalCode ? styles["form-error"] : ""
              }`}
              value={postalCode}
              onChange={handlePostalCodeChange}
              placeholder={getPostalCodePlaceholder()}
            />
            {errors.postalCode && (
              <div className={styles["error-text"]}>{errors.postalCode}</div>
            )}
          </div>
        </div>
      </div>

      <div className={styles["notice"]}>税费将根据您的地址自动计算</div>
    </div>
  );
}

// 检测用户位置的辅助函数
export function detectUserLocation(): Promise<AddressInfo | null> {
  return new Promise((resolve) => {
    // 尝试从浏览器获取地理位置信息
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            // 这里可以使用地理编码服务将坐标转换为地址
            // 暂时返回基于时区的默认国家
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            let country = "US"; // 默认美国

            if (
              timezone.includes("Asia/Shanghai") ||
              timezone.includes("Asia/Hong_Kong")
            ) {
              country = "CN";
            } else if (timezone.includes("Europe")) {
              country = "GB";
            } else if (timezone.includes("Asia/Tokyo")) {
              country = "JP";
            }

            resolve({ country, postalCode: "" });
          } catch (error) {
            resolve(null);
          }
        },
        () => {
          // 地理位置获取失败，使用时区推测
          const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
          let country = "US";

          if (
            timezone.includes("Asia/Shanghai") ||
            timezone.includes("Asia/Hong_Kong")
          ) {
            country = "CN";
          } else if (timezone.includes("Europe")) {
            country = "GB";
          } else if (timezone.includes("Asia/Tokyo")) {
            country = "JP";
          }

          resolve({ country, postalCode: "" });
        },
      );
    } else {
      // 不支持地理位置，使用时区推测
      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      let country = "US";

      if (
        timezone.includes("Asia/Shanghai") ||
        timezone.includes("Asia/Hong_Kong")
      ) {
        country = "CN";
      } else if (timezone.includes("Europe")) {
        country = "GB";
      } else if (timezone.includes("Asia/Tokyo")) {
        country = "JP";
      }

      resolve({ country, postalCode: "" });
    }
  });
}

// 验证地址信息的辅助函数
export function validateAddress(address: AddressInfo | null): boolean {
  if (!address) return false;

  // 至少需要国家信息
  if (!address.country) return false;

  // 美国需要邮编
  if (address.country === "US" && !address.postalCode) {
    return false;
  }

  return true;
}
