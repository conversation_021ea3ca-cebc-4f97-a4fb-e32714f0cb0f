// 豆包首页样式 - 全屏设计
.container {
  width: 100vw;
  height: 100vh;
  background: #fafafa;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

// 顶部导航栏
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 32px;
  background: transparent;
  z-index: 100;
  position: relative;

  @media (max-width: 768px) {
    padding: 16px 20px;
  }
}

.headerLeft {
  display: flex;
  align-items: center;
}

.headerRight {
  display: flex;
  align-items: center;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.downloadButton {
  padding: 8px 16px;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
  }
}

.menuButton {
  padding: 8px;
  border-radius: 8px;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.05);
  }
}

// 用户相关
.userSection {
  position: relative;
}

.userAvatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;

  &:hover {
    border-color: var(--primary);
    transform: scale(1.05);
  }
}

.loginButton {
  padding: 8px 16px;
  background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(29, 147, 171, 0.2);

  &:hover {
    background: linear-gradient(135deg, #1a8299 0%, var(--primary) 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(29, 147, 171, 0.3);
  }
  
  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(29, 147, 171, 0.2);
  }
}

// 用户菜单
.userMenu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.08);
  min-width: 200px;
  z-index: 1000;
  overflow: hidden;
  animation: slideDown 0.2s ease;
}

.menuItem {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: var(--black);
  position: relative;

  &:hover {
    background: rgba(0, 0, 0, 0.04);
  }

  span {
    margin-left: 8px;
  }
}

.menuIcon {
  width: 16px;
  height: 16px;
  opacity: 0.7;
}

.recommended {
  margin-left: auto !important;
  background: #3b82f6;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.menuDivider {
  height: 1px;
  background: rgba(0, 0, 0, 0.08);
  margin: 4px 0;
}

// 主要内容区域
.main {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 24px;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;

  @media (max-width: 768px) {
    padding: 20px px;
  }
}

// 欢迎区域
.welcomeSection {
  text-align: center;
  margin-bottom: 36px;
  padding: 0 20px;

  @media (max-width: 768px) {
    margin-bottom: 40px;
    padding: 0 16px;
  }
}

.title {
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 20px;
  line-height: 1.2;
  letter-spacing: -0.5px;

  @media (max-width: 768px) {
    font-size: 30px;
    margin-bottom: 16px;
  }
}

.subtitle {
  font-size: 18px;
  color: #64748b;
  line-height: 1.6;
  max-width: 640px;
  margin: 0 auto;
  font-weight: 400;

  @media (max-width: 768px) {
    font-size: 16px;
  }
}

// 搜索区域
.searchSection {
  width: 100%;
  max-width: 700px;
  margin-bottom: 40px;
  padding: 0 16px;

  @media (max-width: 768px) {
    margin-bottom: 30px;
    padding: 0;
    width: calc(100% - 40px);
    max-width: none;
    margin-left: auto;
    margin-right: auto;
  }

  @media (max-width: 480px) {
    padding: 0;
    width: calc(100% - 32px);
    margin-left: auto;
    margin-right: auto;
  }
}

.searchBox {
  display: flex;
  align-items: center;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 18px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 68px;
  box-shadow: none;
  position: relative;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;

  &:hover {
    border-color: #cbd5e1;
    background: #f1f5f9;
  }

  &:focus-within {
    border-color: #3b82f6;
    background: white;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  @media (max-width: 768px) {
    padding: 8px 12px;
    min-height: 44px;
    border-radius: 22px;
    margin: 0 auto;
  }
}

.searchPlaceholder {
  flex: 1;
  color: #9ca3af;
  font-size: 16px;
  user-select: none;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    font-size: 15px;
  }
}

.searchActions {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.sendButton {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(29, 147, 171, 0.2);
  
  &:hover {
    background: linear-gradient(135deg, #1a8299 0%, var(--primary) 100%);
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(29, 147, 171, 0.3);
  }
  
  &:active {
    transform: scale(0.95);
    box-shadow: 0 2px 8px rgba(29, 147, 171, 0.2);
  }
  
  @media (max-width: 768px) {
    width: 30px;
    height: 30px;
  }
}

// 快捷操作
.quickActions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  width: 100%;
  max-width: 600px;
  padding: 0 16px;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 12px;
    padding: 0;
    width: calc(100% - 40px);
    max-width: none;
    margin-left: auto;
    margin-right: auto;
  }

  @media (max-width: 480px) {
    padding: 0;
    width: calc(100% - 32px);
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    margin-left: auto;
    margin-right: auto;
  }
}

.actionCard {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 6px 12px;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 50px;
  border: 1px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  gap: 8px;
  min-height: 36px;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    border-color: #cbd5e1;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.12), 0 2px 4px rgba(0, 0, 0, 0.04);
    
    &::before {
      left: 100%;
    }
    
    .actionIcon {
      transform: scale(1.05);
    }
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.08);
  }

  @media (max-width: 768px) {
    padding: 6px 10px;
    gap: 6px;
    min-height: 32px;
  }
}

.actionIcon {
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    font-size: 14px;
  }
}

.actionName {
  font-size: 13px;
  font-weight: 600;
  color: #374151;
  line-height: 1.2;
  flex: 1;
  position: relative;
  z-index: 1;
  letter-spacing: -0.1px;

  @media (max-width: 768px) {
    font-size: 12px;
    font-weight: 500;
  }
}

// 关于创想AI触发器 - 简化样式
.aboutTrigger {
  padding: 8px 0;
  background: transparent;
  border: none;
  color: var(--primary);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  white-space: nowrap;
  font-weight: 600;
  text-decoration: none;
  
  &:hover {
    color: #1a8299;
    text-decoration: underline;
  }
  
  // 深色模式适配
  @media (prefers-color-scheme: dark) {
    color: var(--primary);
    
    &:hover {
      color: #1a8299;
      text-decoration: underline;
    }
  }
}

// 关于豆包气泡框
.aboutMenu {
  position: fixed;
  bottom: 80px;
  left: 20px;
  z-index: 1000;
  animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.aboutContent {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.1);
  min-width: 320px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
  }
}

.aboutHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(147, 51, 234, 0.03) 100%);

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.aboutBody {
  padding: 12px 0;
}

.aboutItem {
  display: flex;
  align-items: center;
  padding: 14px 24px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 15px;
  color: #374151;
  font-weight: 500;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    transform: scaleY(0);
    transition: transform 0.3s ease;
  }

  &:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(147, 51, 234, 0.08) 100%);
    color: #1e293b;
    transform: translateX(4px);
    
    &::before {
      transform: scaleY(1);
    }
  }
  
  a {
    color: inherit;
    text-decoration: none;
    
    &:hover {
      color: #3b82f6;
    }
  }
}

.aboutFooter {
  padding: 20px 24px;
  border-top: 1px solid rgba(59, 130, 246, 0.1);
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(147, 51, 234, 0.03) 100%);

  p {
    margin: 6px 0;
    font-size: 13px;
    color: #64748b;
    line-height: 1.5;
    font-weight: 400;
    
    &:first-child {
      font-weight: 600;
      color: #374151;
    }
  }
}

// 下载移动端气泡框
.downloadMenu {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.2s ease;
  
  &.show {
    opacity: 1;
  }
}

.downloadContent {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24px;
  box-shadow: 0 32px 100px rgba(0, 0, 0, 0.15), 0 16px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.9);
  min-width: 400px;
  max-width: 480px;
  overflow: hidden;
  backdrop-filter: blur(24px);
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #6366f1, #8b5cf6, #06b6d4, #10b981);
  }
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 0%, rgba(99, 102, 241, 0.03) 0%, transparent 50%);
    pointer-events: none;
  }
}

.downloadHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 32px 24px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);

  h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.025em;
  }
}

.downloadBody {
  padding: 24px 0 28px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.downloadItem {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 32px;
  margin: 12px 24px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 16px;
  color: #334155;
  font-weight: 600;
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 2px solid rgba(226, 232, 240, 0.8);
  border-radius: 18px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05), 0 2px 6px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  
  &:first-child {
    margin-top: 20px;
  }
  
  &:nth-child(2) {
    margin-bottom: 8px;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
  }

  &:hover {
    background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    color: white;
    transform: translateY(-4px) scale(1.03);
    box-shadow: 0 16px 40px rgba(99, 102, 241, 0.35), 0 8px 20px rgba(0, 0, 0, 0.12);
    border-color: #6366f1;
    
    &::before {
      left: 100%;
    }
  }
  
  &:active {
    transform: translateY(-2px) scale(1.01);
    box-shadow: 0 10px 28px rgba(99, 102, 241, 0.3);
  }

  span {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    font-size: 18px;
    z-index: 1;
    position: relative;
    
    svg {
      width: 20px;
      height: 20px;
      flex-shrink: 0;
      opacity: 0.9;
      transition: all 0.3s ease;
    }
  }
  
  &:hover span svg {
    opacity: 1;
    transform: scale(1.1);
  }
}

.developmentNote {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 8px 24px 20px;
  // background: linear-gradient(135deg, rgba(251, 146, 60, 0.08) 0%, rgba(245, 101, 101, 0.08) 100%);
  // border: 1px solid rgba(251, 146, 60, 0.2);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    // height: 2px;
    // background: linear-gradient(90deg, #fb923c, #f59e0b, #f97316);
  }
  
  span {
    font-size: 14px;
    font-weight: 500;
    color: #ea580c;
    letter-spacing: 0.5px;
    position: relative;
    z-index: 1;
    
    &::before {
      content: '⚡';
      margin-right: 8px;
      font-size: 16px;
      animation: pulse 2s infinite;
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.closeButton {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background: rgba(148, 163, 184, 0.08);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: #64748b;
  backdrop-filter: blur(8px);

  &:hover {
    background: rgba(239, 68, 68, 0.12);
    color: #ef4444;
    transform: scale(1.1) rotate(90deg);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
  }

  &:active {
    transform: scale(0.95) rotate(90deg);
  }
}



// 侧边栏关于豆包触发按钮 - 简化样式，参考立即登录按钮
.aboutTrigger {
  background: none;
  border: none;
  color: var(--primary);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
  padding: 2px 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  text-align: center;
  
  &:hover {
    background: rgba(29, 147, 171, 0.1);
    color: #1a8299;
  }
  
  &:active {
    transform: scale(0.98);
  }
}

// 遮罩层
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(8px);
  z-index: 999;
  opacity: 0;
  transition: opacity 0.2s ease;
  cursor: pointer;
  
  &.show {
    opacity: 1;
  }
}

// 简化的动画
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 480px) {
  .container {
    padding: 0;
  }

  .main {
    padding: 16px 0px;
  }

  .title {
    font-size: 20px;
  }

  .subtitle {
    font-size: 13px;
  }

  .quickActions {
    grid-template-columns: repeat(2, 1fr);
  }

  .actionCard {
    padding: 12px 8px;
  }

  .aboutMenu,
  .downloadMenu {
    left: 10px;
    right: 10px;
    bottom: 10px;
    top: auto;
  }

  .aboutContent,
  .downloadContent {
    min-width: auto;
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  }

  .header {
    background: rgba(30, 41, 59, 0.8);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }

  .searchBox,
  .actionCard,
  .aboutContent,
  .downloadContent,
  .userMenu {
    background: #1e293b;
    border-color: rgba(255, 255, 255, 0.1);
  }

  .title {
    color: white;
  }

  .subtitle {
    color: #94a3b8;
  }

  .searchPlaceholder {
    color: #64748b;
  }

  .actionName {
    color: white;
  }

  .menuItem,
  .aboutItem,
  .downloadItem {
    color: white;

    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }
  }

  .aboutFooter p {
    color: #64748b;
  }
}

// 容器适配侧边栏
.containerWithSidebar {
  margin-left: var(--sidebar-width);
  width: calc(100vw - var(--sidebar-width));
  transition: all 0.3s ease;
}

// 悬浮菜单按钮
.floatingMenuButton {
  position: fixed;
  top: 20px;
  left: 20px;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateX(-20px);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  &.show {
    opacity: 1;
    transform: translateX(0);
  }

  &:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);

    .floatingMenuTooltip {
      opacity: 1;
      transform: translateX(0);
    }
  }
}

.floatingMenuTooltip {
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%) translateX(-10px);
  margin-left: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: 1001;

  &::before {
    content: '';
    position: absolute;
    left: -4px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
    border-right: 4px solid rgba(0, 0, 0, 0.8);
  }
}