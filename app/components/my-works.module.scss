// 我的作品页面样式 - 现代清新设计
.worksPage {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  z-index: 100;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

// 页面头部 - 现代美观
.pageHeader {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  backdrop-filter: blur(30px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 28px 32px;
  flex-shrink: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.3), transparent);
  }
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.headerLeft {
  h1 {
    margin: 0 0 8px 0;
    font-size: 32px;
    font-weight: 700;
    background: linear-gradient(135deg, #1e293b 0%, #374151 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.03em;
    filter: drop-shadow(0 2px 4px rgba(30, 41, 59, 0.1));
  }

  p {
    margin: 0;
    font-size: 16px;
    color: #64748b;
    font-weight: 400;
    opacity: 0.8;
    letter-spacing: -0.01em;
  }
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 16px;
}

.refreshButton {
  padding: 12px 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #6366f1;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 15px rgba(99, 102, 241, 0.1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    transition: left 0.5s ease;
  }

  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);

    &::before {
      left: 100%;
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    color: #94a3b8;
    border-color: rgba(148, 163, 184, 0.2);
  }
}

.closeButton {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  cursor: pointer;
  font-size: 16px;
  color: #64748b;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  &:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
    color: #475569;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

// 页面内容 - 现代容器
.pageContent {
  flex: 1;
  overflow-y: auto;
  padding: 32px;
  background: transparent;
}

.contentContainer {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

// 顶部提示信息 - 现代美观设计
.noticeBar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  margin-bottom: 24px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(139, 92, 246, 0.08) 100%);
  border: 1px solid rgba(99, 102, 241, 0.15);
  border-radius: 12px;
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  }

  &:hover {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.12) 0%, rgba(139, 92, 246, 0.12) 100%);
    border-color: rgba(99, 102, 241, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
  }
}

.noticeIcon {
  font-size: 18px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 2px 4px rgba(99, 102, 241, 0.2));
}

.noticeText {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  line-height: 1.5;
  letter-spacing: -0.01em;
}

// 加载状态 - 现代设计
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 40px;
  text-align: center;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
}

.loadingSpinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingText {
  font-size: 16px;
  color: #64748b;
  font-weight: 500;
}

// 错误状态 - 现代设计
.errorMessage {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 40px;
  text-align: center;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
}

.errorIcon {
  font-size: 48px;
  margin-bottom: 20px;
  opacity: 0.7;
}

.errorText {
  font-size: 16px;
  color: #ef4444;
  margin-bottom: 24px;
  font-weight: 500;
}

.retryButton {
  padding: 12px 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
  }
}

// 空状态 - 现代设计
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 40px;
  text-align: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.8) 100%);
  backdrop-filter: blur(30px);
  border-radius: 24px;
  border: 1px solid rgba(99, 102, 241, 0.1);
  box-shadow: 0 8px 32px rgba(99, 102, 241, 0.08);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  }
}

.emptyIcon {
  font-size: 64px;
  margin-bottom: 24px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 4px 8px rgba(99, 102, 241, 0.2));
}

.emptyTitle {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #1e293b 0%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 12px;
  letter-spacing: -0.03em;
}

.emptySubtitle {
  font-size: 16px;
  color: #64748b;
  margin-bottom: 36px;
  line-height: 1.6;
  max-width: 400px;
  opacity: 0.8;
  letter-spacing: -0.01em;
}

.createButton {
  padding: 16px 32px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: white;
  border: none;
  border-radius: 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(99, 102, 241, 0.4);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
  }
}

// 作品网格 - 现代响应式设计
.worksGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

// 作品卡片 - 现代玻璃态设计
.workCard {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
    border-color: rgba(255, 255, 255, 0.4);
  }
}

.workImage {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: scale(1.05);
    }
  }
}

.noImage {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: #94a3b8;
  font-size: 14px;
  font-weight: 500;
}

// 状态显示区域 - 现代清新设计
.statusDisplay {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  text-align: center;
  padding: 16px 12px;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
}

// 处理中状态 - 柔和渐变
.statusDisplay.processing {
  background: linear-gradient(135deg, #fafbff 0%, #f6f8ff 50%, #fafbff 100%);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.05), transparent);
    animation: shimmer 2s ease-in-out infinite;
  }
}

// 失败状态
.statusDisplay.failed {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.statusIcon {
  font-size: 36px;
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}

// 处理中图标 - 现代动画
.statusIcon.processing {
  color: #3b82f6;
  animation: modernSpin 2s ease-in-out infinite;
  filter: drop-shadow(0 2px 8px rgba(59, 130, 246, 0.3));
}

// 失败图标
.statusIcon.failed {
  color: #ef4444;
  filter: drop-shadow(0 2px 8px rgba(239, 68, 68, 0.3));
}

.processingHint {
  position: relative;
  z-index: 1;
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 18px;
  margin: 0;
  padding: 24px 16px;
  border-radius: 12px;
}

.processingText {
  font-size: 15px;
  color: #6366f1;
  font-weight: 500;
  opacity: 0.9;
  letter-spacing: -0.01em;
  margin-bottom: 0;
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;

  &::before {
    content: '✨';
    font-size: 16px;
    opacity: 0.8;
    flex-shrink: 0;
    animation: pulse 2s ease-in-out infinite;
  }
}

.manualUpdateButton {
  padding: 8px 16px;
  background: #ffffff;
  color: #3b82f6;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-width: 70px;

  &:hover:not(:disabled) {
    background: #f8fafc;
    border-color: #3b82f6;
    color: #1d4ed8;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }

  &:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  &:disabled {
    cursor: not-allowed;
    transform: none;
  }

  // 成功状态
  &.success {
    background: #f0fdf4;
    color: #16a34a;
    border-color: #bbf7d0;

    &::before {
      content: '✓ ';
      color: #16a34a;
    }
  }

  // 失败状态
  &.failed {
    background: #fef2f2;
    color: #dc2626;
    border-color: #fecaca;

    &::before {
      content: '✕ ';
      color: #dc2626;
    }
  }

  // 禁用状态（刷新中）
  &:disabled:not(.success):not(.failed) {
    background: #f8fafc;
    color: #64748b;
    border-color: #e2e8f0;

    &::before {
      content: '';
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 1px solid #e2e8f0;
      border-top: 1px solid #64748b;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 6px;
      vertical-align: middle;
    }
  }
}

.failedHint {
  font-size: 14px;
  color: #ef4444;
  font-weight: 400;
  position: relative;
  z-index: 1;
  text-align: center;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0;
  opacity: 0.8;
  letter-spacing: 0.01em;
}

// 现代旋转动画
@keyframes modernSpin {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.05); }
  100% { transform: rotate(360deg) scale(1); }
}

// 微妙的流光效果
@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.workOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.6) 100%);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  .workCard:hover & {
    opacity: 1;
  }
}

.viewButton {
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  color: #1e293b;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.workInfo {
  padding: 16px;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
  box-sizing: border-box;
  width: 100%;
}

.workMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.workType {
  font-size: 13px;
  color: #64748b;
  font-weight: 600;
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

.workStatus {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 600;
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

.statusProcessing {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1d4ed8;
  box-shadow: 0 2px 4px rgba(29, 78, 216, 0.2);
}

.statusSuccess {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
  color: #166534;
  box-shadow: 0 2px 4px rgba(22, 101, 52, 0.2);
}

.statusFailed {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #dc2626;
  box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);
}

.statusDefault {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #64748b;
}

.workDetails {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
}

.workId {
  font-size: 12px;
  color: #94a3b8;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-weight: 500;
  letter-spacing: 0.025em;
  flex-shrink: 0;
}

.workTime {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  text-align: right;
  flex-shrink: 0;
}

// 分页 - 现代设计
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 24px;
  padding: 24px;
  margin-top: 32px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.pageButton {
  padding: 10px 18px;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

  &:hover:not(:disabled) {
    background: #f8fafc;
    border-color: #cbd5e1;
    color: #475569;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    background: #f8fafc;
  }
}

.pageInfo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 0 16px;
}

.pageNumbers {
  font-size: 15px;
  color: #374151;
  font-weight: 600;
}

.totalCount {
  font-size: 12px;
  color: #9ca3af;
  font-weight: 500;
}

// 跳转到指定页面
.jumpToPage {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 12px;
}

.jumpLabel {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
  white-space: nowrap;
}

.jumpInput {
  width: 60px;
  padding: 8px 10px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  text-align: center;
  background: #ffffff;
  color: #374151;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &::placeholder {
    color: #9ca3af;
    font-size: 12px;
  }

  // 隐藏数字输入框的箭头
  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  &[type=number] {
    -moz-appearance: textfield;
  }
}

.jumpButton {
  padding: 8px 14px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  }
}

// 响应式设计 - 现代适配
@media (max-width: 1400px) {
  .worksGrid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 1024px) {
  .worksGrid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 18px;
  }

  .pageContent {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .pageHeader {
    padding: 20px 24px;
  }

  .headerContent {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .headerLeft h1 {
    font-size: 24px;
  }

  .headerRight {
    width: 100%;
    justify-content: space-between;
  }

  .pageContent {
    padding: 20px;
  }

  .worksGrid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
  }

  .workImage {
    height: 180px;
  }

  .pagination {
    flex-direction: column;
    gap: 16px;
    padding: 20px;
  }

  .jumpToPage {
    order: -1; // 在移动端将跳转功能放到最上面
  }

  .jumpLabel {
    display: none; // 在小屏幕上隐藏"跳转到"文字
  }

  .jumpInput {
    width: 50px;
  }
}

@media (max-width: 480px) {
  .pageHeader {
    padding: 16px 20px;
  }

  .headerLeft h1 {
    font-size: 20px;
  }

  .pageContent {
    padding: 16px;
  }

  .worksGrid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
  }

  .workImage {
    height: 160px;
  }

  .statusIcon {
    font-size: 32px;
  }

  .statusDisplay {
    padding: 20px;
  }

  .workInfo {
    padding: 16px;
  }

  .emptyState, .loading, .errorMessage {
    padding: 60px 24px;
  }

  .emptyIcon {
    font-size: 48px;
  }

  .emptyTitle {
    font-size: 20px;
  }
}

// 动画关键帧
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}
