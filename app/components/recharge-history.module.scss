@import "../styles/animation.scss";

.history-page {
  width: 100%;
  height: 100vh;
  background-color: var(--white);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: var(--border-in-light);
  background-color: var(--white);
  flex-shrink: 0;

  .header-left {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 20px;

    .page-title {
      text-align: left;

      h1 {
        font-size: 24px;
        font-weight: 600;
        color: var(--black);
        margin: 0 0 4px 0;
      }

      p {
        font-size: 14px;
        color: var(--black-50);
        margin: 0;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .refresh-button,
      .back-button {
        padding: 12px 20px;
        border-radius: 12px;
        border: 2px solid rgba(29, 147, 171, 0.15);
        background: var(--white);
        color: var(--black-70);
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        letter-spacing: 0.3px;
        
        &:hover {
          background: var(--primary);
          border-color: var(--primary);
          color: white;
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(29, 147, 171, 0.2);
        }
        
        &:active {
          transform: translateY(0);
        }
        
        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          transform: none;
          
          &:hover {
            background: var(--white);
            border-color: rgba(29, 147, 171, 0.15);
            color: var(--black-70);
            transform: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
          }
        }
      }
    }
  }

  .close-button {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: var(--white);
    border: 2px solid rgba(29, 147, 171, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 20px;
    color: var(--black-70);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    &:hover {
      background: var(--primary);
      color: white;
      border-color: var(--primary);
      transform: scale(1.05);
      box-shadow: 0 4px 16px rgba(29, 147, 171, 0.2);
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 32px 24px;
  background: linear-gradient(135deg, rgba(29, 147, 171, 0.02) 0%, var(--white) 100%);
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-in-light);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
  
  .loading-text {
    color: var(--black-50);
    font-size: 14px;
  }
}

.error-message {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  margin-bottom: 24px;
  
  .error-icon {
    font-size: 20px;
  }
  
  .error-text {
    flex: 1;
    color: #dc2626;
    font-size: 14px;
  }
  
  .retry-button {
    background-color: #dc2626;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background-color: #b91c1c;
    }
  }
}

.recharge-history-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  position: relative;
  overflow: hidden;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow: hidden;
  min-height: 0; // 确保flex子元素能正确收缩
}

.transactions-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 20px;
    text-align: center;
    
    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }
    
    .empty-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--black);
      margin-bottom: 8px;
    }
    
    .empty-subtitle {
      font-size: 14px;
      color: var(--black-50);
      margin-bottom: 24px;
      max-width: 400px;
      line-height: 1.5;
    }
    
    .go-recharge-button {
      padding: 14px 24px;
      background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4px 16px rgba(29, 147, 171, 0.2);
      letter-spacing: 0.3px;
      
      &:hover {
        background: linear-gradient(135deg, rgba(29, 147, 171, 0.9) 0%, rgba(26, 130, 153, 0.9) 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(29, 147, 171, 0.3);
      }
      
      &:active {
        transform: translateY(0);
      }
    }
  }

  .table-container {
    background: var(--white);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: 1px solid rgba(29, 147, 171, 0.08);
    margin-bottom: 32px;
  }

  .transactions-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    
    thead {
      background: linear-gradient(135deg, rgba(29, 147, 171, 0.05) 0%, rgba(29, 147, 171, 0.02) 100%);
      
      th {
        padding: 20px 24px;
        text-align: left;
        font-weight: 600;
        color: var(--black-70);
        font-size: 13px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 2px solid rgba(29, 147, 171, 0.1);
        position: relative;
        
        &:first-child {
          border-top-left-radius: 16px;
        }
        
        &:last-child {
          border-top-right-radius: 16px;
        }
      }
    }
    
    tbody {
      .table-row {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border-bottom: 1px solid rgba(29, 147, 171, 0.06);
        
        &:hover {
          background: linear-gradient(135deg, rgba(29, 147, 171, 0.02) 0%, rgba(29, 147, 171, 0.01) 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 16px rgba(29, 147, 171, 0.08);
        }
        
        &:last-child {
          border-bottom: none;
        }
        
        td {
          padding: 20px 24px;
          vertical-align: middle;
          
          &.order-id {
            .order-number {
              font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
              font-size: 13px;
              color: var(--black-60);
              background: rgba(29, 147, 171, 0.05);
              padding: 6px 12px;
              border-radius: 8px;
              display: inline-block;
            }
          }
          
          &.amount {
            .amount-value {
              font-size: 18px;
              font-weight: 700;
              background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
            }
          }
          
          &.status {
            .status-badge {
              padding: 8px 16px;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 600;
              letter-spacing: 0.5px;
              text-transform: uppercase;
              display: inline-block;
              
              &.status-success {
                background: linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(34, 197, 94, 0.05) 100%);
                color: rgb(22, 163, 74);
                border: 1px solid rgba(34, 197, 94, 0.2);
              }
              
              &.status-pending {
                background: linear-gradient(135deg, rgba(251, 191, 36, 0.15) 0%, rgba(251, 191, 36, 0.05) 100%);
                color: rgb(217, 119, 6);
                border: 1px solid rgba(251, 191, 36, 0.2);
              }
              
              &.status-failed {
                background: linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(239, 68, 68, 0.05) 100%);
                color: rgb(220, 38, 38);
                border: 1px solid rgba(239, 68, 68, 0.2);
              }
              
              &.status-default {
                background: linear-gradient(135deg, rgba(107, 114, 128, 0.15) 0%, rgba(107, 114, 128, 0.05) 100%);
                color: rgb(75, 85, 99);
                border: 1px solid rgba(107, 114, 128, 0.2);
              }
            }
          }
          
          &.time {
            .time-value {
              color: var(--black-60);
              font-size: 13px;
            }
          }
          
          &.description {
            .description-text {
              color: var(--black-50);
              font-size: 13px;
              max-width: 200px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }
    
    // 响应式设计 - 移动端隐藏表格，显示卡片
    @media (max-width: 768px) {
      display: none;
    }
  }
  
  // 移动端卡片样式
  .mobile-cards {
    display: none;
    
    @media (max-width: 768px) {
      display: block;
      
      .mobile-card {
        background: var(--white);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 16px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
        border: 1px solid rgba(29, 147, 171, 0.08);
        
        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          
          .order-number {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 12px;
            color: var(--black-60);
            background: rgba(29, 147, 171, 0.05);
            padding: 4px 8px;
            border-radius: 6px;
          }
          
          .status-badge {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 11px;
            font-weight: 600;
            letter-spacing: 0.5px;
            text-transform: uppercase;
            
            &.status-success {
              background: linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(34, 197, 94, 0.05) 100%);
              color: rgb(22, 163, 74);
              border: 1px solid rgba(34, 197, 94, 0.2);
            }
            
            &.status-pending {
              background: linear-gradient(135deg, rgba(251, 191, 36, 0.15) 0%, rgba(251, 191, 36, 0.05) 100%);
              color: rgb(217, 119, 6);
              border: 1px solid rgba(251, 191, 36, 0.2);
            }
            
            &.status-failed {
              background: linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(239, 68, 68, 0.05) 100%);
              color: rgb(220, 38, 38);
              border: 1px solid rgba(239, 68, 68, 0.2);
            }
            
            &.status-default {
              background: linear-gradient(135deg, rgba(107, 114, 128, 0.15) 0%, rgba(107, 114, 128, 0.05) 100%);
              color: rgb(75, 85, 99);
              border: 1px solid rgba(107, 114, 128, 0.2);
            }
          }
        }
        
        .card-content {
          .amount-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            
            .amount-label {
              font-size: 13px;
              color: var(--black-50);
            }
            
            .amount-value {
              font-size: 16px;
              font-weight: 700;
              background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
            }
          }
          
          .time-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            
            .time-label {
              font-size: 13px;
              color: var(--black-50);
            }
            
            .time-value {
              font-size: 13px;
              color: var(--black-60);
            }
          }
          
          .description-row {
            .description-label {
              font-size: 13px;
              color: var(--black-50);
              margin-bottom: 4px;
            }
            
            .description-text {
              font-size: 13px;
              color: var(--black-60);
              line-height: 1.4;
            }
          }
        }
      }
    }
  }
}

// 旧的卡片式样式已移除，现在使用表格形式

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(29, 147, 171, 0.08);
  margin-top: 24px;
  
  .pagination-info {
    .total-info {
      font-size: 14px;
      color: var(--black-60);
      font-weight: 500;
    }
  }
  
  .pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .pagination-button {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      background: var(--white);
      border: 2px solid rgba(29, 147, 171, 0.1);
      border-radius: 12px;
      color: var(--black-70);
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      
      .button-icon {
        font-size: 16px;
        font-weight: bold;
      }
      
      &:hover:not(:disabled) {
        background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
        border-color: var(--primary);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(29, 147, 171, 0.2);
      }
      
      &:active:not(:disabled) {
        transform: translateY(0);
      }
      
      &:disabled {
        opacity: 0.4;
        cursor: not-allowed;
        transform: none;
        
        &:hover {
          background: var(--white);
          border-color: rgba(29, 147, 171, 0.1);
          color: var(--black-70);
          transform: none;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }
      }
    }
    
    .page-numbers {
      display: flex;
      align-items: center;
      gap: 4px;
      margin: 0 16px;
      
      .page-number {
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--white);
        border: 2px solid rgba(29, 147, 171, 0.1);
        border-radius: 12px;
        color: var(--black-70);
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        
        &:hover:not(:disabled):not(.active) {
          background: rgba(29, 147, 171, 0.05);
          border-color: rgba(29, 147, 171, 0.2);
          color: var(--primary);
          transform: translateY(-1px);
        }
        
        &.active {
          background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
          border-color: var(--primary);
          color: white;
          box-shadow: 0 4px 16px rgba(29, 147, 171, 0.3);
          transform: translateY(-1px);
        }
        
        &:disabled {
          opacity: 0.4;
          cursor: not-allowed;
        }
      }
    }
  }
  
  .page-info {
    .current-page {
      font-size: 14px;
      color: var(--black-60);
      font-weight: 500;
    }
  }
  
  // 响应式设计
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
    padding: 20px;
    
    .pagination-controls {
      .page-numbers {
        margin: 0 8px;
        
        .page-number {
          width: 40px;
          height: 40px;
          font-size: 13px;
        }
      }
      
      .pagination-button {
        padding: 10px 16px;
        font-size: 13px;
      }
    }
  }
}

// 桌面端表格
.desktop-table {
  background: var(--white);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  overflow: auto;
  animation: slide-in ease 0.3s;
  width: 100%;
  max-width: 100%;
  
  .table {
    width: 100%;
    border-collapse: collapse;
    table-layout: auto;
    min-width: 600px;
    
    th {
      background: linear-gradient(135deg, var(--primary) 0%, rgba(29, 147, 171, 0.8) 100%);
      padding: 16px 12px;
      text-align: left;
      font-weight: bold;
      color: white;
      border-bottom: none;
      font-size: 14px;
      position: sticky;
      top: 0;
      z-index: 10;
    }
    
    td {
      padding: 16px 12px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      color: var(--black);
      font-size: 14px;
      vertical-align: middle;
    }
    
    tr:last-child td {
      border-bottom: none;
    }
    
    tbody tr:hover {
      background: linear-gradient(135deg, rgba(29, 147, 171, 0.05) 0%, rgba(29, 147, 171, 0.02) 100%);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    tbody tr {
      transition: all 0.2s ease;
    }
  }
}

// 移动端卡片
.mobile-cards {
  display: none;
  gap: 16px;
  max-height: calc(100vh - 400px);
  overflow-y: auto;
  
  .transaction-card {
    background: var(--white);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    padding: 20px;
    animation: slide-in ease 0.3s;
    border: 1px solid rgba(29, 147, 171, 0.1);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
      border-color: rgba(29, 147, 171, 0.2);
    }
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      
      .card-type {
        font-weight: bold;
        font-size: 16px;
      }
    }
    
    .card-content {
      .card-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding: 12px 16px;
        background: rgba(29, 147, 171, 0.03);
        border-radius: 12px;
        border: 1px solid rgba(29, 147, 171, 0.08);
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .card-label {
          font-size: 14px;
          color: var(--black-60);
          font-weight: 500;
          flex-shrink: 0;
        }
        
        .card-value {
          font-size: 15px;
          color: var(--black);
          font-weight: 600;
          text-align: right;
          margin-left: 12px;
          
          &.amount {
            color: var(--primary);
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
        }
      }
    }
  }
}

// 类型样式
.type-recharge {
  color: var(--primary);
  font-weight: bold;
}

.type-consumption {
  color: #ff6b35;
  font-weight: bold;
}

// 状态徽章
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
}

.status-success {
  background: rgba(76, 175, 80, 0.1);
  color: #388e3c;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-pending {
  background: rgba(255, 193, 7, 0.1);
  color: #f57c00;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-failed {
  background: rgba(244, 67, 54, 0.1);
  color: #d32f2f;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.status-cancelled {
  background: rgba(158, 158, 158, 0.1);
  color: #616161;
  border: 1px solid rgba(158, 158, 158, 0.3);
}

.status-default {
  background: var(--second);
  color: var(--black);
  border: 1px solid var(--border-in-light);
}

// 分页控制
.pagination {
  margin-top: 24px;
  background: var(--white);
  padding: 16px;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  position: sticky;
  bottom: 0;
  z-index: 5;
  
  .pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
    
    .pagination-btn {
      background: var(--white);
      border: 1px solid var(--border-in-light);
      color: var(--black);
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s ease;
      
      &:hover:not(.disabled) {
        background: var(--hover-color);
        border-color: var(--primary);
      }
      
      &.active {
        background: var(--primary);
        color: white;
        border-color: var(--primary);
      }
      
      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background: var(--second);
      }
    }
    
    .pagination-pages {
      display: flex;
      gap: 4px;
      
      .pagination-page {
        min-width: 40px;
        padding: 8px 12px;
      }
    }
  }
  
  .pagination-info {
    text-align: center;
    font-size: 14px;
    color: var(--black);
    opacity: 0.7;
  }
}

.footer {
  padding: 20px;
  text-align: center;
  background: var(--white);
  border-top: var(--border-in-light);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

// 移动端适配
@media screen and (max-width: 768px) {
  .history-page {
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .page-header {
    padding: 12px 16px;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    border-bottom: 1px solid rgba(29, 147, 171, 0.1);
    flex-shrink: 0;
    min-height: 60px;

    .header-left {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 16px;
      flex: 1;
      min-width: 0;

      .page-title {
        h1 {
          font-size: 18px;
          margin: 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        p {
          display: none;
        }
      }

      .header-actions {
        display: flex;
        gap: 8px;
        
        .refresh-button,
        .back-button {
          padding: 8px 12px;
          font-size: 12px;
          border-radius: 6px;
          white-space: nowrap;
        }
      }
    }

    .close-button {
      width: 36px;
      height: 36px;
      font-size: 16px;
      flex-shrink: 0;
    }
  }

  .page-content {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    display: flex;
    flex-direction: column;
  }
  
  .table-container {
    display: none;
  }
  
  .mobile-cards {
    display: flex;
    flex-direction: column;
    gap: 10px;
    flex: 1;
  }

  .mobile-card {
    padding: 12px;
    border-radius: 8px;
    border: 1px solid rgba(29, 147, 171, 0.1);
    background: white;
    
    .card-header {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .order-number {
        font-size: 11px;
        color: #666;
        font-weight: 500;
      }

      .status-badge {
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 4px;
      }
    }

    .card-content {
      .amount-row,
      .time-row,
      .description-row {
        margin-bottom: 6px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .amount-label,
        .time-label,
        .description-label {
          font-size: 11px;
          color: #666;
        }
        
        .amount-value,
        .time-value,
        .description-text {
          font-size: 12px;
          font-weight: 500;
        }
      }
    }
  }

  .pagination {
    margin-top: 12px;
    padding: 12px 0;
    border-top: 1px solid rgba(29, 147, 171, 0.1);
    flex-shrink: 0;
    
    .pagination-info {
      text-align: center;
      margin-bottom: 8px;
      
      .total-info {
        font-size: 11px;
        color: #666;
      }
    }
    
    .pagination-controls {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 6px;
      flex-wrap: wrap;
      
      .pagination-button {
        padding: 4px 8px;
        font-size: 11px;
        border-radius: 4px;
        min-width: 50px;
        border: 1px solid rgba(29, 147, 171, 0.2);
        background: white;
        
        .button-icon {
          font-size: 12px;
        }

        &:disabled {
          opacity: 0.5;
        }
      }
      
      .page-numbers {
        display: flex;
        gap: 2px;
        
        .page-number {
          width: 28px;
          height: 28px;
          padding: 0;
          font-size: 11px;
          border-radius: 4px;
          border: 1px solid rgba(29, 147, 171, 0.2);
          background: white;

          &.active {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
          }
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 40px 20px;
    
    .empty-icon {
      font-size: 32px;
      margin-bottom: 12px;
    }

    .empty-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .empty-subtitle {
      font-size: 12px;
      color: #666;
      margin-bottom: 16px;
      line-height: 1.4;
    }

    .go-recharge-button {
      padding: 8px 16px;
      font-size: 12px;
      background: var(--primary);
      color: white;
      border: none;
      border-radius: 6px;
    }
  }

  .error-message {
    padding: 12px;
    margin: 12px 0;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    
    .error-text {
      font-size: 12px;
      color: #dc2626;
      flex: 1;
    }
    
    .retry-button {
      padding: 4px 8px;
      font-size: 11px;
      background: #dc2626;
      color: white;
      border: none;
      border-radius: 4px;
    }
  }
}

@media screen and (max-width: 480px) {
  .page-header {
    padding: 8px 12px;
    
    .header-left {
      .page-title {
        h1 {
          font-size: 16px;
        }
        
        p {
          font-size: 11px;
        }
      }
      
      .header-actions {
        .refresh-button,
        .back-button {
          padding: 4px 8px;
          font-size: 11px;
        }
      }
    }
    
    .close-button {
      width: 28px;
      height: 28px;
      font-size: 14px;
    }
  }
  
  .page-content {
    height: calc(100vh - 100px);
    padding: 12px;
  }
  
  .mobile-card {
    padding: 12px;
    
    .card-header {
      .order-number {
        font-size: 11px;
      }
      
      .status-badge {
        font-size: 10px;
        padding: 2px 6px;
      }
    }
    
    .card-content {
      .amount-row,
      .time-row,
      .description-row {
        .amount-label,
        .time-label,
        .description-label {
          font-size: 11px;
        }
        
        .amount-value,
        .time-value,
        .description-text {
          font-size: 12px;
        }
      }
    }
  }
  
  .pagination {
    .pagination-info {
      .total-info {
        font-size: 11px;
      }
    }
    
    .pagination-controls {
      gap: 4px;
      
      .pagination-button {
        padding: 4px 8px;
        font-size: 11px;
        min-width: 50px;
      }
      
      .page-numbers {
        gap: 2px;
        
        .page-number {
          width: 28px;
          height: 28px;
          font-size: 11px;
        }
      }
    }
    
    .page-info {
      .current-page {
        font-size: 10px;
      }
    }
  }
  
  .loading {
    padding: 40px 12px;
    
    .loading-spinner {
      width: 28px;
      height: 28px;
    }
    
    .loading-text {
      font-size: 12px;
    }
  }
  
  .error-message {
    padding: 12px;
    margin: 12px 0;
    
    .error-text {
      font-size: 12px;
    }
    
    .retry-button {
      padding: 6px 12px;
      font-size: 11px;
    }
  }
}