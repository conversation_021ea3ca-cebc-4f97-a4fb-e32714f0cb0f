import { SEO_CONFIG, STRUCTURED_DATA_TEMPLATES } from "../config/seo";

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  structuredData?: any;
  noIndex?: boolean;
}

export function SEOHead({
  title,
  description,
  keywords = [],
  structuredData,
  noIndex = false,
}: SEOProps) {
  const pageTitle = title || SEO_CONFIG.SITE_NAME;
  const pageDescription = description || SEO_CONFIG.SITE_DESCRIPTION;
  const pageKeywords = [...SEO_CONFIG.KEYWORDS, ...keywords].join(", ");

  return (
    <>
      {/* 基本SEO标签 */}
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      <meta name="keywords" content={pageKeywords} />

      {/* 机器人指令 */}
      {noIndex ? (
        <meta name="robots" content="noindex, nofollow" />
      ) : (
        <meta
          name="robots"
          content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1"
        />
      )}

      {/* Open Graph */}
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content={SEO_CONFIG.SITE_URL} />
      <meta property="og:site_name" content={SEO_CONFIG.SITE_NAME} />
      <meta
        property="og:image"
        content={`${SEO_CONFIG.SITE_URL}${SEO_CONFIG.IMAGES.og}`}
      />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={pageTitle} />
      <meta property="og:locale" content="zh_CN" />

      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={pageDescription} />
      <meta
        name="twitter:image"
        content={`${SEO_CONFIG.SITE_URL}${SEO_CONFIG.IMAGES.og}`}
      />
      <meta name="twitter:creator" content={SEO_CONFIG.SOCIAL.twitter} />

      {/* 其他重要标签 */}
      <meta name="author" content={SEO_CONFIG.AUTHOR.name} />
      <meta name="publisher" content={SEO_CONFIG.SITE_NAME} />
      <meta
        name="copyright"
        content={`© ${new Date().getFullYear()} ${SEO_CONFIG.SITE_NAME}`}
      />
      <meta name="language" content="zh-CN" />
      <meta name="revisit-after" content="1 days" />
      <meta name="distribution" content="global" />
      <meta name="rating" content="general" />

      {/* 移动端优化 */}
      <meta name="format-detection" content="telephone=no" />
      <meta name="format-detection" content="email=no" />
      <meta name="format-detection" content="address=no" />

      {/* 结构化数据 */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html:
              typeof structuredData === "string"
                ? structuredData
                : JSON.stringify(structuredData),
          }}
        />
      )}

      {/* 默认结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: STRUCTURED_DATA_TEMPLATES.WebApplication({}),
        }}
      />
    </>
  );
}

// 面包屑导航组件
interface BreadcrumbItem {
  name: string;
  url: string;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
}

export function BreadcrumbStructuredData({ items }: BreadcrumbProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: items.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name,
      item: `${SEO_CONFIG.SITE_URL}${item.url}`,
    })),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
}

// FAQ结构化数据组件
interface FAQItem {
  question: string;
  answer: string;
}

interface FAQProps {
  faqs: FAQItem[];
}

export function FAQStructuredData({ faqs }: FAQProps) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: STRUCTURED_DATA_TEMPLATES.FAQPage(faqs),
      }}
    />
  );
}

// 文章结构化数据组件
interface ArticleProps {
  title: string;
  description: string;
  author: string;
  datePublished: string;
  dateModified?: string;
  image?: string;
}

export function ArticleStructuredData({
  title,
  description,
  author,
  datePublished,
  dateModified,
  image,
}: ArticleProps) {
  const structuredData = STRUCTURED_DATA_TEMPLATES.Article({
    headline: title,
    description,
    author: {
      "@type": "Person",
      name: author,
    },
    datePublished,
    dateModified: dateModified || datePublished,
    image: image
      ? `${SEO_CONFIG.SITE_URL}${image}`
      : `${SEO_CONFIG.SITE_URL}${SEO_CONFIG.IMAGES.og}`,
  });

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: structuredData,
      }}
    />
  );
}
