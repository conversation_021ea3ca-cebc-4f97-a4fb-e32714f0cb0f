@import "../styles/animation.scss";

.recharge-page {
  width: 100%;
  height: 100vh;
  background-color: var(--white);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: var(--border-in-light);
  background-color: var(--white);
  flex-shrink: 0;

  .header-left {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 20px;

    .page-title {
      text-align: left;

      h1 {
        font-size: 24px;
        font-weight: 600;
        color: var(--black);
        margin: 0 0 4px 0;
      }

      p {
        font-size: 14px;
        color: var(--black-50);
        margin: 0;
      }
    }

    .header-actions {
      .history-button {
        padding: 12px 20px;
        border-radius: 12px;
        border: 2px solid rgba(29, 147, 171, 0.15);
        background: var(--white);
        color: var(--black-70);
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        letter-spacing: 0.3px;
        
        &:hover {
          background: var(--primary);
          border-color: var(--primary);
          color: white;
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(29, 147, 171, 0.2);
        }
        
        &:active {
          transform: translateY(0);
        }
      }
    }
  }

  .close-button {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: var(--white);
    border: 2px solid rgba(29, 147, 171, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 20px;
    color: var(--black-70);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    
    &:hover {
      background: var(--primary);
      color: white;
      border-color: var(--primary);
      transform: scale(1.05);
      box-shadow: 0 4px 16px rgba(29, 147, 171, 0.2);
    }
    
    &:active {
      transform: scale(0.95);
    }
  }
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.balance-section {
  padding: 20px;
  margin: 20px;
  background: var(--white);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  text-align: center;
  animation: slide-in ease 0.3s;
  
  .balance-title {
    font-size: 16px;
    color: var(--black);
    margin-bottom: 8px;
  }
  
  .balance-amount {
    font-size: 32px;
    font-weight: bold;
    color: var(--primary);
    
    .coins-text {
      font-size: 16px;
      margin-left: 8px;
      color: var(--black);
      opacity: 0.7;
    }
  }
}

.packages-container {
  padding: 32px 24px;
  max-width: 1200px;
  margin: 0 auto;
  
  .packages-header {
    text-align: center;
    margin-bottom: 40px;
    
    .packages-title {
      font-size: 28px;
      font-weight: 700;
      color: var(--black);
      margin-bottom: 12px;
      background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .packages-subtitle {
      font-size: 16px;
      color: var(--black-50);
      line-height: 1.5;
      max-width: 600px;
      margin: 0 auto;
    }
  }
  
  .packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
  }
}

.package-card {
  background: var(--white);
  border-radius: 16px;
  padding: 32px 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slide-in ease 0.3s;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary) 0%, #1a8299 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(29, 147, 171, 0.15);
    
    &::before {
      opacity: 1;
    }
  }
  
  &.selected {
    border-color: var(--primary);
    background: linear-gradient(135deg, rgba(29, 147, 171, 0.05) 0%, var(--white) 100%);
    box-shadow: 0 8px 32px rgba(29, 147, 171, 0.2);
    
    &::before {
      opacity: 1;
    }
    
    .package-title {
      color: var(--primary);
    }
    
    .package-price {
      color: var(--primary);
    }
  }
  
  &.popular {
    border-color: var(--primary);
    transform: scale(1.05);
    
    &::after {
      content: "最受欢迎";
      position: absolute;
      top: -1px;
      right: -1px;
      background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
      color: white;
      padding: 8px 16px;
      border-radius: 0 16px 0 16px;
      font-size: 12px;
      font-weight: 600;
      letter-spacing: 0.5px;
    }
    
    &:hover {
      transform: scale(1.05) translateY(-8px);
    }
  }
  
  &.custom-package {
    border: 2px dashed var(--border-in-light);
    background: linear-gradient(135deg, rgba(29, 147, 171, 0.02) 0%, var(--white) 100%);
    
    &:hover {
      border-color: var(--primary);
      background: linear-gradient(135deg, rgba(29, 147, 171, 0.05) 0%, var(--white) 100%);
      box-shadow: 0 8px 32px rgba(29, 147, 171, 0.15);
    }
    
    &.selected {
      border-style: solid;
      border-color: var(--primary);
      background: linear-gradient(135deg, rgba(29, 147, 171, 0.08) 0%, var(--white) 100%);
      box-shadow: 0 12px 40px rgba(29, 147, 171, 0.2);
    }
    
    .package-title {
      color: var(--primary);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      
      &::before {
        content: "💰";
        font-size: 18px;
      }
    }
  }
  
  .package-header {
    text-align: center;
    margin-bottom: 24px;
    
    .package-title {
      font-size: 20px;
      font-weight: 600;
      color: var(--black);
      margin-bottom: 12px;
      letter-spacing: 0.5px;
    }
    
    .package-description {
      font-size: 14px;
      color: var(--black);
      opacity: 0.7;
    }
  }
  
  .package-content {
    text-align: center;
    
    .coins-amount {
      font-size: 36px;
      font-weight: bold;
      color: var(--primary);
      margin-bottom: 8px;
      
      .coins-label {
        font-size: 16px;
        margin-left: 8px;
        color: var(--black);
        opacity: 0.7;
      }
    }
    
    .price {
      font-size: 32px;
      font-weight: 700;
      color: var(--primary);
      margin-bottom: 8px;
      background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      
      .currency {
        font-size: 20px;
        margin-right: 4px;
      }
    }
    
    .original-price {
      font-size: 16px;
      color: var(--black-50);
      text-decoration: line-through;
      font-weight: 400;
      margin-bottom: 12px;
    }
    
    .value-info {
      font-size: 14px;
      color: var(--black-60);
      margin-bottom: 20px;
      line-height: 1.4;
    }
    
    // 自定义金额输入相关样式
    .custom-input-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      padding: 8px;
      
      .input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        max-width: 280px;
        margin: 0 auto;
        
        .currency-symbol {
          position: absolute;
          left: 20px;
          top: 50%;
          transform: translateY(-50%);
          font-size: 22px;
          font-weight: 700;
          color: var(--primary);
          z-index: 1;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          pointer-events: none;
        }
        
        .custom-amount-input {
          width: 100%;
          padding: 20px 20px 20px 50px;
          border: 2px solid var(--border-in-light);
          border-radius: 16px;
          font-size: 20px;
          font-weight: 600;
          text-align: center;
          background: var(--white);
          color: var(--black);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
          
          &:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 4px rgba(29, 147, 171, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
          }
          
          &:hover:not(:focus) {
            border-color: rgba(29, 147, 171, 0.4);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
          }
          
          &::placeholder {
            color: var(--black-50);
            font-weight: 400;
            font-size: 18px;
          }
          
          // 移除数字输入框的箭头
          &::-webkit-outer-spin-button,
          &::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
          }
          
          &[type=number] {
            -moz-appearance: textfield;
          }
        }
      }
      
      .custom-error {
        font-size: 13px;
        color: var(--red);
        text-align: center;
        padding: 8px 16px;
        background: rgba(223, 27, 65, 0.08);
        border-radius: 12px;
        border: 1px solid rgba(223, 27, 65, 0.15);
        font-weight: 500;
        max-width: 280px;
        width: 100%;
        box-shadow: 0 2px 8px rgba(223, 27, 65, 0.1);
      }
      
      .custom-preview {
        font-size: 15px;
        color: var(--primary);
        font-weight: 600;
        text-align: center;
        padding: 12px 20px;
        background: linear-gradient(135deg, rgba(29, 147, 171, 0.12) 0%, rgba(29, 147, 171, 0.06) 100%);
        border-radius: 12px;
        border: 1px solid rgba(29, 147, 171, 0.2);
        max-width: 280px;
        width: 100%;
        box-shadow: 0 2px 8px rgba(29, 147, 171, 0.1);
        backdrop-filter: blur(10px);
      }
    }
  }
}

.payment-section {
  padding: 20px;
  margin: 0 20px 20px;
  background: var(--white);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  animation: slide-in ease 0.3s;
  
  // Stripe Checkout模式不需要支付表单样式
  
  .selected-package {
    text-align: center;
    margin-bottom: 20px;
    padding: 16px;
    background: var(--second);
    border-radius: 8px;
    
    .selected-title {
      font-size: 16px;
      font-weight: bold;
      color: var(--black);
      margin-bottom: 8px;
    }
    
    .selected-details {
      font-size: 14px;
      color: var(--black);
      opacity: 0.8;
    }
  }
  
  .payment-button {
    width: 100%;
    padding: 14px 24px;
    background: linear-gradient(135deg, var(--primary) 0%, #1a8299 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 15px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 12px;
    box-shadow: 0 4px 16px rgba(29, 147, 171, 0.2);
    letter-spacing: 0.3px;
    
    &:hover:not(:disabled) {
      background: linear-gradient(135deg, rgba(29, 147, 171, 0.9) 0%, rgba(26, 130, 153, 0.9) 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(29, 147, 171, 0.3);
    }
    
    &:active:not(:disabled) {
      transform: translateY(0);
    }
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }
    
    .loading-spinner {
      display: inline-block;
      width: 16px;
      height: 16px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: white;
      animation: spin 1s ease-in-out infinite;
      margin-right: 8px;
    }
  }
  
  .security-info {
    text-align: center;
    font-size: 12px;
    color: var(--black);
    opacity: 0.6;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    
    .security-icon {
      width: 16px;
      height: 16px;
      opacity: 0.7;
    }
  }
}

.error-message {
  background: rgba(255, 0, 0, 0.1);
  border: 1px solid rgba(255, 0, 0, 0.3);
  color: #d32f2f;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  text-align: center;
  font-size: 14px;
}

.success-message {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #388e3c;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  text-align: center;
  font-size: 14px;
}

// 加载状态样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: var(--white);
  margin: 20px;
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  animation: slide-in ease 0.3s;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(29, 147, 171, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 20px;
  }
  
  .loading-text {
    font-size: 16px;
    color: var(--black);
    opacity: 0.7;
    text-align: center;
    animation: pulse 2s ease-in-out infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

@media screen and (max-width: 768px) {
  .packages-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .package-card {
    padding: 20px;
    
    .package-content {
      .coins-amount {
        font-size: 28px;
      }
      
      .price {
        font-size: 20px;
      }
    }
  }
  
  .balance-section {
    margin: 16px;
    padding: 16px;
    
    .balance-amount {
      font-size: 24px;
    }
  }
  
  .packages-container {
    padding: 0 16px 16px;
  }
  
  .payment-section {
    margin: 0 16px 16px;
    padding: 16px;
  }
  
  .loading-container {
    margin: 16px;
    padding: 60px 16px;
    
    .loading-spinner {
      width: 36px;
      height: 36px;
    }
    
    .loading-text {
      font-size: 14px;
    }
  }
}

@media screen and (max-width: 480px) {
  .header {
    padding: 16px;
    
    .title {
      font-size: 20px;
    }
  }
  
  .package-card {
    padding: 16px;
    
    .package-content {
      .coins-amount {
        font-size: 24px;
      }
      
      .price {
        font-size: 18px;
      }
      
      // 移动端自定义金额输入适配
      .custom-input-container {
        gap: 12px;
        padding: 4px;
        
        .input-wrapper {
          max-width: 240px;
          margin: 0 auto;
          
          .currency-symbol {
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 18px;
            font-weight: 700;
            pointer-events: none;
          }
          
          .custom-amount-input {
            padding: 16px 16px 16px 42px;
            font-size: 18px;
            border-radius: 14px;
            
            &::placeholder {
              font-size: 16px;
            }
          }
        }
        
        .custom-error {
          font-size: 12px;
          padding: 6px 12px;
          max-width: 240px;
          border-radius: 10px;
        }
        
        .custom-preview {
          font-size: 13px;
          padding: 8px 16px;
          max-width: 240px;
          border-radius: 10px;
        }
      }
    }
    
    &.custom-package {
      .package-title {
        font-size: 16px;
        
        &::before {
          font-size: 16px;
        }
      }
    }
  }
}

// 自定义弹框样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease;
}

.payment-modal {
  background: var(--white);
  border-radius: 16px;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideInScale 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.success-modal,
.fail-modal {
  background: var(--white);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  max-width: 480px;
  width: 90vw;
  max-height: 80vh;
  overflow: hidden;
  animation: slideInScale 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  
  .modal-header {
    padding: 32px 32px 24px;
    text-align: center;
    position: relative;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    
    .success-icon,
    .fail-icon {
      width: 64px;
      height: 64px;
      margin: 0 auto 16px;
      animation: bounceIn 0.6s ease;
      
      svg {
        width: 100%;
        height: 100%;
        filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.15));
      }
    }
    
    .modal-title {
      font-size: 24px;
      font-weight: 700;
      color: var(--black);
      margin: 0;
      letter-spacing: 0.5px;
    }
    
    .modal-close {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 36px;
      height: 36px;
      border: none;
      background: rgba(0, 0, 0, 0.05);
      border-radius: 50%;
      color: var(--black-60);
      font-size: 18px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      
      &:hover {
        background: rgba(0, 0, 0, 0.1);
        color: var(--black);
        transform: scale(1.1);
      }
    }
  }
  
  .modal-content {
    padding: 24px 32px;
    
    .success-message,
    .fail-message {
      font-size: 16px;
      color: var(--black-70);
      text-align: center;
      margin: 0 0 24px;
      line-height: 1.6;
    }
    
    .success-details {
      background: linear-gradient(135deg, rgba(76, 175, 80, 0.05) 0%, rgba(76, 175, 80, 0.02) 100%);
      border-radius: 12px;
      padding: 20px;
      border: 1px solid rgba(76, 175, 80, 0.1);
      
      .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .detail-label {
          font-size: 14px;
          color: var(--black-60);
          font-weight: 500;
        }
        
        .detail-value {
          font-size: 16px;
          color: var(--primary);
          font-weight: 600;
        }
      }
    }
    
    .fail-tips {
      background: linear-gradient(135deg, rgba(244, 67, 54, 0.05) 0%, rgba(244, 67, 54, 0.02) 100%);
      border-radius: 12px;
      padding: 20px;
      border: 1px solid rgba(244, 67, 54, 0.1);
      
      h4 {
        font-size: 16px;
        color: var(--black);
        margin: 0 0 12px;
        font-weight: 600;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          font-size: 14px;
          color: var(--black-60);
          margin-bottom: 8px;
          line-height: 1.5;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
  
  .modal-footer {
    padding: 20px 32px 32px;
    display: flex;
    gap: 12px;
    justify-content: center;
    
    .success-button {
      flex: 1;
      max-width: 200px;
      padding: 14px 24px;
      background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4px 16px rgba(76, 175, 80, 0.3);
      letter-spacing: 0.5px;
      
      &:hover {
        background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
      }
      
      &:active {
        transform: translateY(0);
      }
    }
    
    .fail-button-secondary {
      flex: 1;
      padding: 14px 24px;
      background: var(--white);
      color: var(--black-70);
      border: 2px solid rgba(0, 0, 0, 0.1);
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(0, 0, 0, 0.05);
        border-color: rgba(0, 0, 0, 0.2);
      }
    }
    
    .fail-button-primary {
      flex: 1;
      padding: 14px 24px;
      background: linear-gradient(135deg, #F44336 0%, #d32f2f 100%);
      color: white;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4px 16px rgba(244, 67, 54, 0.3);
      letter-spacing: 0.5px;
      
      &:hover {
        background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
      }
      
      &:active {
        transform: translateY(0);
      }
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 移动端适配
@media screen and (max-width: 600px) {
  .success-modal,
  .fail-modal {
    width: 95vw;
    max-width: none;
    
    .modal-header {
      padding: 24px 20px 20px;
      
      .success-icon,
      .fail-icon {
        width: 56px;
        height: 56px;
        margin-bottom: 12px;
      }
      
      .modal-title {
        font-size: 20px;
      }
    }
    
    .modal-content {
      padding: 20px;
      
      .success-message,
      .fail-message {
        font-size: 15px;
      }
      
      .success-details,
      .fail-tips {
        padding: 16px;
      }
    }
    
    .modal-footer {
      padding: 16px 20px 24px;
      flex-direction: column;
      
      .success-button,
      .fail-button-secondary,
      .fail-button-primary {
        max-width: none;
        width: 100%;
      }
    }
  }
}