"use client";

import { useState } from "react";
import { useNavigate } from "react-router-dom";
import styles from "./register.module.scss";
import { IconButton } from "./button";
import { Path } from "../constant";
import Locale from "../locales";
import LeftIcon from "../icons/left.svg";
import DeepSeekIcon from "../icons/llm-icons/deepseek.svg";
import EyeIcon from "../icons/eye.svg";
import EyeOffIcon from "../icons/eye-off.svg";
// 移除图标导入，使用文字标识
import { useMobileScreen } from "../utils";
import { API, RegisterRequest } from "../api/custom-api";
import clsx from "clsx";

interface RegisterFormData {
  account: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export function RegisterPage() {
  const navigate = useNavigate();
  const isMobile = useMobileScreen();
  const [formData, setFormData] = useState<RegisterFormData>({
    account: "",
    email: "",
    password: "",
    confirmPassword: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  const handleInputChange = (field: keyof RegisterFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (error) setError(""); // Clear error when user starts typing
  };

  const validateForm = (): boolean => {
    if (!formData.account.trim()) {
      setError("请输入账号");
      return false;
    }
    if (formData.account.trim().length < 2) {
      setError("账号至少2个字符");
      return false;
    }
    if (!formData.email.trim()) {
      setError("请输入邮箱地址");
      return false;
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      setError("请输入有效的邮箱地址");
      return false;
    }
    if (!formData.password.trim()) {
      setError("请输入密码");
      return false;
    }
    if (formData.password.length < 8) {
      setError("密码长度至少8位");
      return false;
    }
    if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(formData.password)) {
      setError("密码必须包含字母和数字");
      return false;
    }
    if (formData.password !== formData.confirmPassword) {
      setError("两次输入的密码不一致");
      return false;
    }
    return true;
  };

  const handleRegister = async () => {
    if (!validateForm()) return;

    setLoading(true);
    setError("");

    try {
      const requestData: RegisterRequest = {
        account: formData.account.trim(),
        password: formData.password,
        email: formData.email.trim(),
        sourceType: 4,
      };

      await API.auth.register(requestData);

      // 显示成功弹框
      setShowSuccessModal(true);
      setError("");
    } catch (err: any) {
      console.error("Register error:", err);
      setError(err.message || "网络错误，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !loading) {
      handleRegister();
    }
  };

  return (
    <div className={styles["register-page"]}>
      <div className={styles["register-container"]}>
        {/* Header */}
        <div className={styles["register-header"]}>
          <IconButton
            icon={<LeftIcon />}
            text="返回"
            onClick={() => navigate(Path.Home)}
            className={styles["back-button"]}
          />
        </div>

        {/* Logo and Title */}
        <div className={styles["register-brand"]}>
          {/* <div className={clsx("no-dark", styles["register-logo"])}>
            <DeepSeekIcon />
          </div> */}
          <h1 className={styles["register-title"]}>创建账户</h1>
          <p className={styles["register-subtitle"]}>
            加入我们，开始您的AI之旅
          </p>
        </div>

        {/* Register Form */}
        <div className={styles["register-form"]}>
          {error && <div className={styles["error-message"]}>{error}</div>}

          <div className={styles["form-group"]}>
            <div className={styles["input-wrapper"]}>
              <span className={styles["input-icon"]}>👤</span>
              <input
                type="text"
                className={styles["form-input"]}
                placeholder="输入您的账号"
                value={formData.account}
                onChange={(e) => handleInputChange("account", e.target.value)}
                onKeyPress={handleKeyPress}
                disabled={loading}
              />
            </div>
          </div>

          <div className={styles["form-group"]}>
            <div className={styles["input-wrapper"]}>
              <span className={styles["input-icon"]}>📧</span>
              <input
                type="email"
                className={styles["form-input"]}
                placeholder="输入您的电子邮箱"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                onKeyPress={handleKeyPress}
                disabled={loading}
              />
            </div>
          </div>

          <div className={styles["form-group"]}>
            <div className={styles["input-wrapper"]}>
              <span className={styles["input-icon"]}>🔒</span>
              <div className={styles["password-input-wrapper"]}>
                <input
                  type={showPassword ? "text" : "password"}
                  className={styles["form-input"]}
                  placeholder="输入您的密码"
                  value={formData.password}
                  onChange={(e) =>
                    handleInputChange("password", e.target.value)
                  }
                  onKeyPress={handleKeyPress}
                  disabled={loading}
                />
                <button
                  type="button"
                  className={styles["password-toggle"]}
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={loading}
                >
                  {showPassword ? "🙈" : "👁️"}
                </button>
              </div>
            </div>
          </div>

          <div className={styles["form-group"]}>
            <div className={styles["input-wrapper"]}>
              <span className={styles["input-icon"]}>🔑</span>
              <div className={styles["password-input-wrapper"]}>
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  className={styles["form-input"]}
                  placeholder="再次输入您的密码"
                  value={formData.confirmPassword}
                  onChange={(e) =>
                    handleInputChange("confirmPassword", e.target.value)
                  }
                  onKeyPress={handleKeyPress}
                  disabled={loading}
                />
                <button
                  type="button"
                  className={styles["password-toggle"]}
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  disabled={loading}
                >
                  {showConfirmPassword ? "🙈" : "👁️"}
                </button>
              </div>
            </div>
          </div>

          <div className={styles["form-actions"]}>
            <button
              className={clsx(styles["register-button"], {
                [styles["loading"]]: loading,
              })}
              onClick={handleRegister}
              disabled={loading}
            >
              {loading ? "注册中..." : "创建账户"}
            </button>
          </div>

          <div className={styles["form-footer"]}>
            <p className={styles["login-link"]}>
              已有账户？
              <button
                type="button"
                className={styles["link-button"]}
                onClick={() => navigate(Path.Login)}
                disabled={loading}
              >
                立即登录
              </button>
            </p>
          </div>

          {/* 成功注册弹框 */}
          {showSuccessModal && (
            <div className={styles["modal-overlay"]}>
              <div className={styles["success-modal"]}>
                <div className={styles["modal-header"]}>
                  <div className={styles["success-icon"]}>✓</div>
                  <h3 className={styles["modal-title"]}>注册成功</h3>
                </div>
                <div className={styles["modal-body"]}>
                  <p className={styles["success-text"]}>恭喜您注册成功！</p>
                  <p className={styles["account-info"]}>
                    您的登录账号是：
                    <span className={styles["account-highlight"]}>
                      {formData.account}
                    </span>
                  </p>
                </div>
                <div className={styles["modal-footer"]}>
                  <button
                    className={styles["confirm-button"]}
                    onClick={() => {
                      setShowSuccessModal(false);
                      navigate(Path.Login);
                    }}
                  >
                    确定
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
