import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { getUserInfo, AuthApi } from "../api/custom-api/auth-api";
import { Path } from "../constant";
import styles from "./user-avatar.module.scss";
import Locale from "../locales";
import { IconButton } from "./button";
import LoginIcon from "../icons/login.svg";

// 用户头像下拉菜单组件
export function UserAvatar() {
  const navigate = useNavigate();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [userInfo, setUserInfo] = useState(getUserInfo());
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // 处理登出
  const handleLogout = async () => {
    try {
      await AuthApi.logout();
      setUserInfo(null);
      setIsDropdownOpen(false);
      navigate(Path.Login);
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  // 跳转到我的账户页面
  const handleMyAccount = () => {
    setIsDropdownOpen(false);
    navigate(Path.MyAccount);
  };

  // 跳转到我的作品页面
  const handleMyWorks = () => {
    setIsDropdownOpen(false);
    navigate(Path.MyWorks);
  };

  // 跳转到登录页面
  const handleLogin = () => {
    navigate(Path.Login);
  };

  // 如果用户未登录，不显示任何内容
  if (!userInfo) {
    return null;
  }

  // 生成用户头像（使用用户名首字母）
  const getAvatarText = (name: string) => {
    return name ? name.charAt(0).toUpperCase() : "U";
  };

  return (
    <div className={styles["user-avatar-container"]} ref={dropdownRef}>
      <div
        className={styles["user-avatar"]}
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
      >
        <div className={styles["avatar-circle"]}>
          {getAvatarText(userInfo.account)}
        </div>
      </div>

      {isDropdownOpen && (
        <div className={styles["dropdown-menu"]}>
          <div className={styles["dropdown-item"]} onClick={handleMyAccount}>
            <span>我的账户</span>
          </div>
          <div className={styles["dropdown-item"]} onClick={handleMyWorks}>
            <span>我的作品</span>
          </div>
          <div className={styles["dropdown-divider"]}></div>
          <div className={styles["dropdown-item"]} onClick={handleLogout}>
            <span>退出登录</span>
          </div>
        </div>
      )}
    </div>
  );
}
