/* eslint-disable @next/next/no-page-custom-font */
import "./styles/globals.scss";
import "./styles/markdown.scss";
import "./styles/highlight.scss";
import { getClientConfig } from "./config/client";
import type { Metadata, Viewport } from "next";
import { SpeedInsights } from "@vercel/speed-insights/next";
import { GoogleTagManager, GoogleAnalytics } from "@next/third-parties/google";
import { getServerSideConfig } from "./config/server";

export const metadata: Metadata = {
  title: "Creative AI - 智能AI聊天助手 | 免费在线AI对话平台",
  description:
    "Creative AI是一个强大的AI聊天助手平台，支持多种AI模型包括DeepSeek、GPT等。提供免费在线AI对话服务，智能问答，文本生成，代码编程助手。立即体验最先进的人工智能技术。",
  keywords: [
    "Creative AI",
    "创想AI",
    "AI聊天",
    "人工智能",
    "ChatGPT",
    "DeepSeek",
    "AI助手",
    "智能对话",
    "免费AI",
    "在线AI",
    "AI问答",
    "代码助手",
    "文本生成",
    "机器学习",
  ],
  authors: [{ name: "Creative AI Team" }],
  creator: "Creative AI",
  publisher: "Creative AI",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://www.51creativeai.com"),
  alternates: {
    canonical: "/",
    languages: {
      "zh-CN": "/zh",
      "en-US": "/en",
      "ja-JP": "/ja",
      "ko-KR": "/ko",
    },
  },
  openGraph: {
    type: "website",
    locale: "zh_CN",
    url: "https://www.51creativeai.com",
    title: "Creative AI - 智能AI聊天助手",
    description:
      "体验最先进的AI聊天技术，支持多种AI模型，免费在线使用。智能对话、代码编程、文本生成一站式AI服务平台。",
    siteName: "Creative AI",
    images: [
      {
        url: "/apple-touch-icon.png",
        width: 180,
        height: 180,
        alt: "Creative AI - 智能AI聊天助手",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Creative AI - 智能AI聊天助手",
    description:
      "免费在线AI对话平台，支持多种AI模型，智能问答、代码编程、文本生成。",
    images: ["/apple-touch-icon.png"],
    creator: "@CreativeAI",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "c28010632c538d3c",
  },
  appleWebApp: {
    title: "Creative AI",
    statusBarStyle: "default",
    capable: true,
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#fafafa" },
    { media: "(prefers-color-scheme: dark)", color: "#151515" },
  ],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const serverConfig = getServerSideConfig();

  return (
    <html lang="en">
      <head>
        <meta
          name="version"
          content={getClientConfig()?.version || "unknown"}
        />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
        />
        <link rel="manifest" href="/site.webmanifest" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

        <meta name="theme-color" content="#151515" />
        <meta name="application-name" content="Creative AI" />
        <meta name="apple-mobile-web-app-title" content="Creative AI" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebApplication",
              name: "Creative AI",
              description:
                "智能AI聊天助手平台，支持多种AI模型，提供免费在线AI对话服务",
              url: "https://www.51creativeai.com",
              applicationCategory: "UtilityApplication",
            }),
          }}
        />
      </head>
      <body>
        {children}
        {serverConfig?.isVercel && (
          <>
            <SpeedInsights />
          </>
        )}
        {serverConfig?.gtmId && (
          <>
            <GoogleTagManager gtmId={serverConfig.gtmId} />
          </>
        )}
        {serverConfig?.gaId && (
          <>
            <GoogleAnalytics gaId={serverConfig.gaId} />
          </>
        )}
      </body>
    </html>
  );
}
