// Lightweight polyfill loader
export const loadPolyfillsIfNeeded = () => {
  // Check if Array.prototype.at is supported
  if (!Array.prototype.at) {
    Array.prototype.at = function (index: number) {
      const length = this.length;
      if (index < 0) {
        index = length + index;
      }
      if (index < 0 || index >= length) {
        return undefined;
      }
      return this[index];
    };
  }
};

// Auto-load polyfills when module is imported
if (typeof window !== "undefined") {
  loadPolyfillsIfNeeded();
}
