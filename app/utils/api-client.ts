/**
 * 统一的API客户端封装
 * 处理请求拦截、响应拦截、错误处理、token管理、加解密等
 */

import { clearAuthToken, getAuthToken } from "./auth-token";
import {
  buildEncryptedRequest,
  handleEncryptedResponse,
  isEncryptionReady,
  type EncryptionOptions,
} from "./sm-crypto";

// 基础响应接口
export interface BaseResponse<T = any> {
  code: string;
  message: string;
  data?: T;
}

// 错误类型枚举
export enum ErrorCode {
  TOKEN_EXPIRED = "B0301",
  KEY_EXPIRED = "B2907",
  TOKEN_INVALID = "TOKEN_INVALID",
  UNAUTHORIZED = "UNAUTHORIZED",
  FORBIDDEN = "FORBIDDEN",
  NOT_FOUND = "NOT_FOUND",
  VALIDATION_ERROR = "VALIDATION_ERROR",
  SERVER_ERROR = "SERVER_ERROR",
  NETWORK_ERROR = "NETWORK_ERROR",
}

// 自定义API错误类
export class ApiError extends Error {
  public code: string;
  public status?: number;
  public data?: any;

  constructor(message: string, code: string, status?: number, data?: any) {
    super(message);
    this.name = "ApiError";
    this.code = code;
    this.status = status;
    this.data = data;
  }
}

// 请求配置接口
export interface RequestConfig {
  headers?: Record<string, string>;
  timeout?: number;
  withAuth?: boolean; // 是否需要携带认证token
  withEncryption?: boolean; // 是否需要加密处理，默认true
  encryptionOptions?: EncryptionOptions;
}

// API客户端类
export class ApiClient {
  private baseURL: string;
  private defaultTimeout: number = 10000;

  constructor(baseURL?: string) {
    // 在客户端环境中，使用默认的代理路径
    // 在服务端环境中，可以使用环境变量
    this.baseURL =
      baseURL ||
      (typeof window !== "undefined"
        ? "/api/proxy/custom"
        : process.env.NEXT_CUSTOM_API_BASE_URL) ||
      "/api";
  }

  // 构建完整URL
  private buildURL(endpoint: string): string {
    if (endpoint.startsWith("http")) {
      return endpoint;
    }
    return `${this.baseURL}${endpoint.startsWith("/") ? "" : "/"}${endpoint}`;
  }

  // 构建请求头
  private buildHeaders(config: RequestConfig = {}): Record<string, string> {
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      ...config.headers,
    };

    // 添加认证头
    if (config.withAuth !== false) {
      const token = getAuthToken();
      if (token) {
        headers["Authorization"] = `${token}`;
      }
    }

    return headers;
  }

  // 处理响应
  private async handleResponse<T>(
    response: Response,
  ): Promise<BaseResponse<T>> {
    let data: any;
    let text: string = "";

    try {
      text = await response.text();

      // 检查响应是否为空
      if (!text) {
        data = {};
      } else {
        // 检查响应是否为HTML（通常表示服务器错误或重定向）
        if (
          text.trim().startsWith("<!DOCTYPE") ||
          text.trim().startsWith("<html")
        ) {
          console.error(
            "[API Client] Received HTML response instead of JSON:",
            text.substring(0, 200),
          );
          throw new ApiError(
            "服务器返回了错误页面，请检查API地址和网络连接",
            ErrorCode.SERVER_ERROR,
            response.status,
          );
        }

        data = JSON.parse(text);
      }
    } catch (error) {
      // 如果是JSON解析错误，提供更详细的错误信息
      if (error instanceof SyntaxError) {
        console.error("[API Client] JSON parse error:", error.message);
        console.error("[API Client] Response text:", text.substring(0, 500));
        throw new ApiError(
          `响应格式错误: ${error.message}`,
          ErrorCode.SERVER_ERROR,
          response.status,
        );
      }

      // 如果是我们自己抛出的ApiError，直接重新抛出
      if (error instanceof ApiError) {
        throw error;
      }

      // 其他未知错误
      throw new ApiError(
        "响应处理失败",
        ErrorCode.SERVER_ERROR,
        response.status,
      );
    }

    // 处理HTTP错误状态
    if (!response.ok) {
      const errorCode = this.getErrorCodeFromStatus(response.status);
      throw new ApiError(
        data.message || `HTTP ${response.status} Error`,
        data.code || errorCode,
        response.status,
        data,
      );
    }

    // 检查业务逻辑错误（code不为'00000'表示失败）
    if (data.code && data.code !== "00000") {
      // 处理token过期或私钥过期情况
      if (
        data.code === ErrorCode.TOKEN_EXPIRED ||
        data.code === ErrorCode.KEY_EXPIRED
      ) {
        // 清除本地token
        clearAuthToken();
        // 刷新页面
        window.location.href = "/#/login";
        throw new ApiError(
          "登录过期，请重新登录",
          data.code,
          response.status,
          data,
        );
      } else {
        throw new ApiError(
          data.message || "请求失败",
          data.code,
          response.status,
          data,
        );
      }
    }

    return data;
  }

  // 根据HTTP状态码获取错误码
  private getErrorCodeFromStatus(status: number): string {
    switch (status) {
      case 401:
        return ErrorCode.UNAUTHORIZED;
      case 403:
        return ErrorCode.FORBIDDEN;
      case 404:
        return ErrorCode.NOT_FOUND;
      case 422:
        return ErrorCode.VALIDATION_ERROR;
      case 500:
      case 502:
      case 503:
      case 504:
        return ErrorCode.SERVER_ERROR;
      default:
        return ErrorCode.SERVER_ERROR;
    }
  }

  // 处理请求数据加密
  private processRequestData(data: any, config: RequestConfig): any {
    // 默认启用加密，除非明确设置为false
    const shouldEncrypt = config.withEncryption !== false;

    if (shouldEncrypt) {
      // 检查加密环境是否就绪
      if (!isEncryptionReady()) {
        throw new ApiError(
          "加密环境未就绪，请先初始化加密环境",
          ErrorCode.SERVER_ERROR,
        );
      }

      // 构建加密请求，如果没有数据则使用空字符串
      const requestData = data || "";
      return buildEncryptedRequest(requestData);
    }
    return data;
  }

  // 处理响应数据解密
  private processResponseData<T>(data: any, config: RequestConfig): T {
    // 默认启用加密，除非明确设置为false
    const shouldDecrypt = config.withEncryption !== false;

    if (shouldDecrypt && data) {
      try {
        return handleEncryptedResponse<T>(data);
      } catch (error) {
        throw new ApiError(
          `响应解密失败: ${
            error instanceof Error ? error.message : String(error)
          }`,
          ErrorCode.SERVER_ERROR,
        );
      }
    }
    return data;
  }

  // 核心请求方法
  public async request<T = any>(
    endpoint: string,
    options: RequestInit = {},
    config: RequestConfig = {},
  ): Promise<BaseResponse<T>> {
    const url = this.buildURL(endpoint);
    let headers = this.buildHeaders(config);
    const timeout = config.timeout || this.defaultTimeout;

    // 使用传入的options（数据加密已在HTTP方法中处理）
    const processedOptions = { ...options };

    // 如果body是FormData，删除Content-Type头，让浏览器自动设置
    if (processedOptions.body instanceof FormData) {
      const { "Content-Type": _, ...headersWithoutContentType } = headers;
      headers = headersWithoutContentType;
    }

    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...processedOptions,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      const result = await this.handleResponse<T>(response);

      // 处理响应数据解密
      if (result.data) {
        result.data = this.processResponseData<T>(result.data, config);
      }

      return result;
    } catch (error: any) {
      clearTimeout(timeoutId);

      // 处理网络错误
      if (error instanceof TypeError || error.name === "AbortError") {
        throw new ApiError(
          error.name === "AbortError" ? "请求超时" : "网络连接失败",
          ErrorCode.NETWORK_ERROR,
        );
      }

      throw error;
    }
  }

  // GET请求
  public async get<T = any>(
    endpoint: string,
    params?: Record<string, any>,
    config?: RequestConfig,
  ): Promise<T> {
    // 先处理参数加密
    const processedData = this.processRequestData(params, config || {});

    let url = endpoint;
    if (processedData) {
      const searchParams = new URLSearchParams();
      Object.entries(processedData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
      url += `?${searchParams.toString()}`;
    }

    const response = await this.request<T>(url, { method: "GET" }, config);
    return response.data as T;
  }

  // POST请求
  public async post<T = any>(
    endpoint: string,
    data?: any,
    config?: RequestConfig,
  ): Promise<T> {
    let body: string | FormData | undefined;

    // 如果是FormData，直接使用，不进行加密处理
    if (data instanceof FormData) {
      body = data;
    } else {
      // 先处理数据加密
      const processedData = this.processRequestData(data, config || {});
      body = processedData ? JSON.stringify(processedData) : undefined;
    }

    const response = await this.request<T>(
      endpoint,
      {
        method: "POST",
        body,
      },
      config,
    );
    return response.data as T;
  }

  // PUT请求
  public async put<T = any>(
    endpoint: string,
    data?: any,
    config?: RequestConfig,
  ): Promise<T> {
    // 先处理数据加密
    const processedData = this.processRequestData(data, config || {});

    const response = await this.request<T>(
      endpoint,
      {
        method: "PUT",
        body: processedData ? JSON.stringify(processedData) : undefined,
      },
      config,
    );
    return response.data as T;
  }

  // DELETE请求
  public async delete<T = any>(
    endpoint: string,
    config?: RequestConfig,
  ): Promise<T> {
    // 先处理数据加密（DELETE请求通常没有请求体，但需要走加密流程）
    const processedData = this.processRequestData(undefined, config || {});

    const response = await this.request<T>(
      endpoint,
      {
        method: "DELETE",
        body: processedData ? JSON.stringify(processedData) : undefined,
      },
      config,
    );
    return response.data as T;
  }

  // PATCH请求
  public async patch<T = any>(
    endpoint: string,
    data?: any,
    config?: RequestConfig,
  ): Promise<T> {
    // 先处理数据加密
    const processedData = this.processRequestData(data, config || {});

    const response = await this.request<T>(
      endpoint,
      {
        method: "PATCH",
        body: processedData ? JSON.stringify(processedData) : undefined,
      },
      config,
    );
    return response.data as T;
  }
}

// 创建默认的API客户端实例
export const apiClient = new ApiClient();

// 导出便捷方法
export const { get, post, put, delete: del, patch } = apiClient;
