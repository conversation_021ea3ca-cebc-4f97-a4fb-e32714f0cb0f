/**
 * SSL配置工具
 * 用于处理生产环境中的SSL证书验证问题
 */

// SSL配置选项
export interface SSLConfig {
  rejectUnauthorized: boolean;
  timeout?: number;
  keepAlive?: boolean;
  keepAliveMsecs?: number;
  checkServerIdentity?: (host: string, cert: any) => Error | undefined;
}

// 获取SSL配置
export function getSSLConfig(url: string): SSLConfig {
  const isProduction = process.env.NODE_ENV === "production";
  const isDevelopment = process.env.NODE_ENV === "development";

  // 基础配置
  const baseConfig: SSLConfig = {
    rejectUnauthorized: true, // 默认启用SSL验证以确保安全性
    timeout: 30000,
    keepAlive: true,
    keepAliveMsecs: 1000,
  };

  // 如果是特定的域名，可以进行特殊处理
  const hostname = new URL(url).hostname;

  // 对于已知的安全域名，可以启用SSL验证
  const trustedDomains = [
    "api.openai.com",
    "api.anthropic.com",
    "generativelanguage.googleapis.com",
  ];

  if (trustedDomains.includes(hostname)) {
    return {
      ...baseConfig,
      rejectUnauthorized: true, // 对于信任的域名启用SSL验证
    };
  }

  // 对于本地开发环境，提供更宽松的SSL配置
  if (hostname.includes("localhost") || hostname.includes("127.0.0.1")) {
    return {
      ...baseConfig,
      rejectUnauthorized: false, // 仅对本地开发禁用SSL验证
      checkServerIdentity: () => undefined, // 跳过服务器身份验证
    };
  }

  // 对于生产环境的自定义API域名，启用SSL验证
  if (hostname.includes("51creativeai.com")) {
    return {
      ...baseConfig,
      rejectUnauthorized: true, // 对生产环境启用SSL验证
    };
  }

  return baseConfig;
}

// 检查是否是SSL相关错误
export function isSSLError(error: Error): boolean {
  const sslErrorPatterns = [
    "certificate",
    "SSL",
    "TLS",
    "CERT_",
    "self signed",
    "unable to verify",
    "DEPTH_ZERO_SELF_SIGNED_CERT",
    "UNABLE_TO_VERIFY_LEAF_SIGNATURE",
    "SELF_SIGNED_CERT_IN_CHAIN",
  ];

  return sslErrorPatterns.some((pattern) =>
    error.message.toLowerCase().includes(pattern.toLowerCase()),
  );
}

// 获取SSL错误的友好提示
export function getSSLErrorMessage(error: Error, url: string): string {
  const hostname = new URL(url).hostname;

  if (isSSLError(error)) {
    return (
      `SSL证书验证失败 (${hostname}): ${error.message}. \n` +
      `建议解决方案:\n` +
      `1. 检查目标服务器的SSL证书配置\n` +
      `2. 如果是自签名证书，请在环境变量中配置 NODE_TLS_REJECT_UNAUTHORIZED=0\n` +
      `3. 使用HTTP代理服务器\n` +
      `4. 联系服务器管理员更新SSL证书`
    );
  }

  return error.message;
}

// 环境变量配置检查
export function checkSSLEnvironment(): void {
  const nodeEnv = process.env.NODE_ENV;
  const tlsReject = process.env.NODE_TLS_REJECT_UNAUTHORIZED;

  console.log("[SSL Config] Environment check:");
  console.log("[SSL Config] NODE_ENV:", nodeEnv);
  console.log("[SSL Config] NODE_TLS_REJECT_UNAUTHORIZED:", tlsReject);

  if (tlsReject === "0") {
    console.warn(
      "[SSL Config] WARNING: SSL certificate verification is globally disabled",
    );
  }
}
