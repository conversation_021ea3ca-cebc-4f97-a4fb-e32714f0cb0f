/**
 * 基于 browser-image-compression 库的图片压缩工具
 * 更稳定、可靠的前端图片压缩解决方案
 */

import imageCompression from "browser-image-compression";

// 压缩配置接口
export interface ImageCompressionOptions {
  /** 最大文件大小（MB），默认1MB */
  maxSizeMB?: number;
  /** 最大宽度，默认1920 */
  maxWidthOrHeight?: number;
  /** 是否使用Web Worker，默认true */
  useWebWorker?: boolean;
  /** 图片质量（0-1），默认0.8 */
  initialQuality?: number;
  /** 输出格式，默认保持原格式 */
  fileType?: string;
  /** 是否保持Exif信息，默认false */
  preserveExif?: boolean;
  /** 最大迭代次数，默认10 */
  maxIteration?: number;
  /** 是否启用调试模式，默认false */
  debug?: boolean;
}

// 压缩结果接口
export interface CompressionResult {
  /** 压缩后的文件 */
  file: File;
  /** 原始文件大小（字节） */
  originalSize: number;
  /** 压缩后文件大小（字节） */
  compressedSize: number;
  /** 压缩比例 */
  compressionRatio: number;
  /** 处理时间（毫秒） */
  processingTime: number;
  /** 文件信息 */
  info: {
    originalName: string;
    compressedName: string;
    originalType: string;
    compressedType: string;
  };
}

// 预设配置
export const IMAGE_COMPRESSION_PRESETS = {
  /** 高质量：适合重要图片，文件较大 */
  HIGH_QUALITY: {
    maxSizeMB: 2,
    maxWidthOrHeight: 2048,
    initialQuality: 0.9,
    useWebWorker: true,
  },

  /** 标准质量：通用场景，平衡质量和大小 */
  STANDARD: {
    maxSizeMB: 1,
    maxWidthOrHeight: 1920,
    initialQuality: 0.8,
    useWebWorker: true,
  },

  /** 快速上传：优先考虑文件大小和上传速度 */
  FAST_UPLOAD: {
    maxSizeMB: 0.5,
    maxWidthOrHeight: 1280,
    initialQuality: 0.7,
    useWebWorker: true,
  },

  /** 头像场景：小尺寸，适合头像上传 */
  AVATAR: {
    maxSizeMB: 0.2,
    maxWidthOrHeight: 512,
    initialQuality: 0.8,
    useWebWorker: true,
  },

  /** 缩略图：极小文件，快速加载 */
  THUMBNAIL: {
    maxSizeMB: 0.1,
    maxWidthOrHeight: 300,
    initialQuality: 0.7,
    useWebWorker: true,
  },
} as const;

/**
 * 压缩单张图片
 * @param file 图片文件
 * @param options 压缩选项
 * @returns Promise<CompressionResult>
 */
export async function compressImage(
  file: File,
  options: ImageCompressionOptions = {},
): Promise<CompressionResult> {
  const startTime = performance.now();

  // 验证输入
  if (!file) {
    throw new Error("文件不能为空");
  }

  if (!file.type.startsWith("image/")) {
    throw new Error("请选择图片文件");
  }

  // 合并默认配置
  const config: ImageCompressionOptions = {
    preserveExif: false,
    maxIteration: 10,
    debug: false,
    ...IMAGE_COMPRESSION_PRESETS.STANDARD,
    ...options,
  };

  try {
    const originalSize = file.size;

    // 如果文件已经很小，可能不需要压缩
    if (
      originalSize <= config.maxSizeMB! * 1024 * 1024 &&
      !config.maxWidthOrHeight
    ) {
      const endTime = performance.now();
      return {
        file,
        originalSize,
        compressedSize: originalSize,
        compressionRatio: 1,
        processingTime: endTime - startTime,
        info: {
          originalName: file.name,
          compressedName: file.name,
          originalType: file.type,
          compressedType: file.type,
        },
      };
    }

    // 执行压缩
    const compressedFile = await imageCompression(file, config);
    const endTime = performance.now();

    const result: CompressionResult = {
      file: compressedFile,
      originalSize,
      compressedSize: compressedFile.size,
      compressionRatio: compressedFile.size / originalSize,
      processingTime: endTime - startTime,
      info: {
        originalName: file.name,
        compressedName: compressedFile.name,
        originalType: file.type,
        compressedType: compressedFile.type,
      },
    };

    // 调试信息
    if (config.debug) {
      console.log("图片压缩完成:", {
        原始文件: `${file.name} (${formatFileSize(originalSize)})`,
        压缩后: `${compressedFile.name} (${formatFileSize(
          compressedFile.size,
        )})`,
        压缩比: `${(result.compressionRatio * 100).toFixed(1)}%`,
        节省空间: formatFileSize(originalSize - compressedFile.size),
        处理时间: `${result.processingTime.toFixed(2)}ms`,
      });
    }

    return result;
  } catch (error) {
    console.error("图片压缩失败:", error);

    // 提供更友好的错误信息
    let errorMessage = "图片压缩失败";
    if (error instanceof Error) {
      if (error.message.includes("not supported")) {
        errorMessage = "不支持的图片格式";
      } else if (error.message.includes("too large")) {
        errorMessage = "图片文件过大";
      } else if (error.message.includes("corrupted")) {
        errorMessage = "图片文件已损坏";
      } else {
        errorMessage = `图片压缩失败: ${error.message}`;
      }
    }

    throw new Error(errorMessage);
  }
}

/**
 * 批量压缩图片
 * @param files 图片文件数组
 * @param options 压缩选项
 * @param onProgress 进度回调
 * @returns Promise<CompressionResult[]>
 */
export async function compressImages(
  files: File[],
  options: ImageCompressionOptions = {},
  onProgress?: (current: number, total: number, currentFile: string) => void,
): Promise<CompressionResult[]> {
  if (!files || files.length === 0) {
    return [];
  }

  const results: CompressionResult[] = [];
  const total = files.length;

  // 过滤出图片文件
  const imageFiles = files.filter((file) => file.type.startsWith("image/"));

  if (imageFiles.length !== files.length) {
    console.warn(`过滤掉 ${files.length - imageFiles.length} 个非图片文件`);
  }

  // 批量处理，限制并发数量避免内存问题
  const concurrencyLimit = 3;

  for (let i = 0; i < imageFiles.length; i += concurrencyLimit) {
    const batch = imageFiles.slice(i, i + concurrencyLimit);

    const batchPromises = batch.map(async (file, batchIndex) => {
      const currentIndex = i + batchIndex;

      try {
        onProgress?.(currentIndex + 1, total, file.name);

        const result = await compressImage(file, options);

        console.log(`✓ ${file.name} 压缩完成 (${currentIndex + 1}/${total})`);
        return { success: true as const, result, file: file.name };
      } catch (error) {
        console.error(`✗ ${file.name} 压缩失败:`, error);
        return {
          success: false as const,
          error: error instanceof Error ? error.message : "未知错误",
          file: file.name,
        };
      }
    });

    const batchResults = await Promise.all(batchPromises);

    // 收集成功的结果
    batchResults.forEach((result) => {
      if (result.success) {
        results.push(result.result);
      }
    });
  }

  const successCount = results.length;
  console.log(`批量压缩完成: ${successCount}/${imageFiles.length} 成功`);

  return results;
}

/**
 * 获取图片信息（不压缩）
 * @param file 图片文件
 * @returns Promise<图片信息>
 */
export async function getImageInfo(file: File) {
  if (!file.type.startsWith("image/")) {
    throw new Error("请选择图片文件");
  }

  return new Promise<{
    width: number;
    height: number;
    size: number;
    type: string;
    name: string;
  }>((resolve, reject) => {
    const img = new Image();
    const url = URL.createObjectURL(file);

    img.onload = () => {
      URL.revokeObjectURL(url);
      resolve({
        width: img.width,
        height: img.height,
        size: file.size,
        type: file.type,
        name: file.name,
      });
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      reject(new Error("无法读取图片信息"));
    };

    img.src = url;
  });
}

/**
 * 创建图片预览URL
 * @param file 图片文件
 * @returns 预览URL
 */
export function createImagePreview(file: File): string {
  if (!file.type.startsWith("image/")) {
    throw new Error("请选择图片文件");
  }

  return URL.createObjectURL(file);
}

/**
 * 释放预览URL
 * @param url 预览URL
 */
export function revokeImagePreview(url: string): void {
  URL.revokeObjectURL(url);
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化的文件大小字符串
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

/**
 * 检查浏览器是否支持Web Worker
 * @returns 是否支持
 */
export function isWebWorkerSupported(): boolean {
  return typeof Worker !== "undefined";
}

/**
 * 检查文件类型是否支持
 * @param file 文件
 * @returns 是否支持
 */
export function isSupportedImageType(file: File): boolean {
  const supportedTypes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/webp",
    "image/bmp",
    "image/gif",
  ];

  return supportedTypes.includes(file.type.toLowerCase());
}
