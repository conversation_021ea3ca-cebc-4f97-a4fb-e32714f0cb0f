/**
 * 统一错误处理工具
 * 处理API错误并显示用户友好的错误信息
 */

import { ApiError } from "./api-client";

// 错误信息映射
const ERROR_MESSAGES: Record<string, string> = {
  // 通用错误
  NETWORK_ERROR: "网络连接失败，请检查网络后重试",
  SERVER_ERROR: "服务器错误，请稍后重试",
  UNAUTHORIZED: "登录已过期，请重新登录",
  FORBIDDEN: "权限不足，无法执行此操作",
  NOT_FOUND: "请求的资源不存在",
  VALIDATION_ERROR: "请求参数有误，请检查后重试",

  // 用户相关错误
  USER_NOT_FOUND: "用户不存在",
  USER_ALREADY_EXISTS: "用户已存在",
  INVALID_CREDENTIALS: "用户名或密码错误",
  EMAIL_NOT_VERIFIED: "邮箱未验证，请先验证邮箱",
  PASSWORD_TOO_WEAK: "密码强度不足，请使用更复杂的密码",

  // 支付相关错误
  PAYMENT_FAILED: "支付失败，请重试或更换支付方式",
  PAYMENT_CANCELED: "支付已取消",
  INVALID_PACKAGE: "无效的套餐选择",
  INSUFFICIENT_BALANCE: "余额不足",
  PAYMENT_PROCESSING: "支付正在处理中，请稍候",

  // 文件上传错误
  FILE_TOO_LARGE: "文件大小超出限制",
  INVALID_FILE_TYPE: "不支持的文件类型",
  UPLOAD_FAILED: "文件上传失败，请重试",
};

// 错误处理选项
export interface ErrorHandlerOptions {
  showToast?: boolean; // 是否显示Toast提示
  showModal?: boolean; // 是否显示模态框
  logError?: boolean; // 是否记录错误日志
  fallbackMessage?: string; // 默认错误信息
  onError?: (error: ApiError) => void; // 自定义错误处理回调
}

// 默认错误处理选项
const DEFAULT_OPTIONS: ErrorHandlerOptions = {
  showToast: true,
  showModal: false,
  logError: true,
  fallbackMessage: "操作失败，请重试",
};

// 错误处理器类
export class ErrorHandler {
  // 处理API错误
  static handle(error: any, options: ErrorHandlerOptions = {}): void {
    const opts = { ...DEFAULT_OPTIONS, ...options };

    // 记录错误日志
    if (opts.logError) {
      console.error("API Error:", error);
    }

    let message = opts.fallbackMessage!;
    let errorCode = "UNKNOWN_ERROR";

    // 解析错误信息
    if (error instanceof ApiError) {
      errorCode = error.code;
      message = ERROR_MESSAGES[error.code] || error.message || message;
    } else if (error instanceof Error) {
      message = error.message || message;
    } else if (typeof error === "string") {
      message = error;
    }

    // 执行自定义错误处理
    if (opts.onError && error instanceof ApiError) {
      opts.onError(error);
    }

    // 显示错误提示
    if (opts.showToast) {
      this.showToast(message);
    }

    if (opts.showModal) {
      this.showModal(message, errorCode);
    }
  }

  // 显示Toast提示
  private static showToast(message: string): void {
    // 这里可以集成具体的Toast组件
    // 暂时使用console.warn作为占位
    console.warn("Toast:", message);

    // 如果有全局Toast组件，可以这样调用：
    // import { showToast } from "../components/ui-lib";
    // showToast(message);
  }

  // 显示模态框
  private static showModal(message: string, errorCode: string): void {
    // 这里可以集成具体的Modal组件
    // 暂时使用alert作为占位
    alert(`错误: ${message}\n错误码: ${errorCode}`);

    // 如果有全局Modal组件，可以这样调用：
    // import { showModal } from "../components/ui-lib";
    // showModal({ title: "错误", content: message });
  }

  // 获取用户友好的错误信息
  static getErrorMessage(error: any): string {
    if (error instanceof ApiError) {
      return ERROR_MESSAGES[error.code] || error.message || "操作失败";
    } else if (error instanceof Error) {
      return error.message || "操作失败";
    } else if (typeof error === "string") {
      return error;
    }
    return "操作失败，请重试";
  }

  // 检查是否为认证错误
  static isAuthError(error: any): boolean {
    return (
      error instanceof ApiError &&
      (error.code === "UNAUTHORIZED" || error.code === "TOKEN_EXPIRED")
    );
  }

  // 检查是否为网络错误
  static isNetworkError(error: any): boolean {
    return error instanceof ApiError && error.code === "NETWORK_ERROR";
  }
}

// 便捷方法
export const handleApiError = ErrorHandler.handle;
export const getErrorMessage = ErrorHandler.getErrorMessage;
export const isAuthError = ErrorHandler.isAuthError;
export const isNetworkError = ErrorHandler.isNetworkError;

// 创建带错误处理的API调用包装器
export function withErrorHandler<T extends (...args: any[]) => Promise<any>>(
  apiCall: T,
  options?: ErrorHandlerOptions,
): T {
  return (async (...args: any[]) => {
    try {
      return await apiCall(...args);
    } catch (error) {
      ErrorHandler.handle(error, options);
      throw error; // 重新抛出错误，让调用方可以进行额外处理
    }
  }) as T;
}
