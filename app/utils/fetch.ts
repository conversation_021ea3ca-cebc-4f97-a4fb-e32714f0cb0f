import { getServerSideConfig } from "@/app/config/server";
import {
  getSSLConfig,
  isSSLError,
  getSSLErrorMessage,
  checkSSLEnvironment,
} from "./ssl-config";

// 动态导入node-fetch（仅在Node.js环境中）
let nodeFetch: any = null;
let Agent: any = null;

async function getNodeFetch() {
  if (!nodeFetch && isNodeEnvironment()) {
    try {
      const fetchModule = await eval('import("node-fetch")');
      nodeFetch = fetchModule.default;
      const httpsModule = await eval('import("https")');
      Agent = httpsModule.default.Agent;
    } catch (error) {
      console.warn("[Proxy] Failed to load node-fetch:", error);
    }
  }
  return nodeFetch;
}

// 检查是否在Node.js环境中（更严格的检测）
function isNodeEnvironment(): boolean {
  return (
    typeof process !== "undefined" &&
    process.versions &&
    !!process.versions.node &&
    typeof require !== "undefined" &&
    !process.env.NEXT_RUNTIME
  );
}

// 动态导入代理模块（仅在Node.js环境中）
async function createProxyAgent(proxyUrl: string, targetUrl: string) {
  // 双重检查：确保在Node.js环境且不是Edge Runtime
  if (!isNodeEnvironment() || process.env.NEXT_RUNTIME === "edge") {
    return null;
  }

  try {
    // 使用eval来避免webpack静态分析
    const httpsProxyAgent = await eval('import("https-proxy-agent")');
    const httpProxyAgent = await eval('import("http-proxy-agent")');
    const socksProxyAgent = await eval('import("socks-proxy-agent")');

    const { HttpsProxyAgent } = httpsProxyAgent;
    const { HttpProxyAgent } = httpProxyAgent;
    const { SocksProxyAgent } = socksProxyAgent;

    const url = new URL(targetUrl);

    // 使用SSL配置工具获取合适的配置
    const sslConfig = getSSLConfig(targetUrl);
    const agentOptions = {
      ...sslConfig,
    };

    if (proxyUrl.startsWith("socks")) {
      return new SocksProxyAgent(proxyUrl, agentOptions);
    } else if (url.protocol === "https:") {
      return new HttpsProxyAgent(proxyUrl, agentOptions);
    } else {
      return new HttpProxyAgent(proxyUrl, agentOptions);
    }
  } catch (error) {
    console.warn("[Proxy] Failed to load proxy modules:", error);
    return null;
  }
}

// 创建支持代理的fetch函数
export async function proxyFetch(
  url: string,
  options: RequestInit = {},
): Promise<Response> {
  const fetchOptions: any = { ...options };

  // 获取代理配置，优先使用PROXY_URL
  const serverConfig = getServerSideConfig();
  const proxyUrl =
    serverConfig.proxyUrl || process.env.HTTP_PROXY || process.env.HTTPS_PROXY;

  if (proxyUrl && isNodeEnvironment()) {
    console.log("[Proxy] Using proxy:", proxyUrl);

    // 检查是否是反向代理URL（包含完整路径）
    if (proxyUrl.includes("/deepseek-proxy") || proxyUrl.includes("/api/")) {
      // 反向代理模式：直接替换URL
      const targetUrl = new URL(url);
      const proxyUrlObj = new URL(proxyUrl);

      // 构建新的URL：代理基础URL + 原始路径
      const newUrl = `${proxyUrlObj.origin}${proxyUrlObj.pathname}${targetUrl.pathname}${targetUrl.search}`;
      console.log("[Proxy] Reverse proxy mode, redirecting to:", newUrl);

      // 为反向代理设置SSL配置
      if (isNodeEnvironment() && newUrl.startsWith("https://")) {
        try {
          const https = await eval('import("https")');
          const sslConfig = getSSLConfig(newUrl);
          fetchOptions.agent = new https.default.Agent(sslConfig);
          console.log("[Proxy] Applied SSL config for reverse proxy:", newUrl);
        } catch (error) {
          console.warn(
            "[Proxy] Failed to setup HTTPS agent for reverse proxy:",
            error,
          );
        }
      }

      return fetch(newUrl, fetchOptions);
    } else {
      // 标准HTTP代理模式
      try {
        const agent = await createProxyAgent(proxyUrl, url);
        if (agent) {
          // @ts-ignore
          fetchOptions.agent = agent;
        }
      } catch (error) {
        console.warn("[Proxy] Failed to setup proxy agent:", error);
      }
    }
  } else {
    // 没有代理时，为HTTPS请求设置SSL配置
    if (isNodeEnvironment() && url.startsWith("https://")) {
      try {
        const https = await eval('import("https")');
        const sslConfig = getSSLConfig(url);
        fetchOptions.agent = new https.default.Agent(sslConfig);
        console.log(
          "[Proxy] Applied SSL config for direct HTTPS request:",
          url,
        );
      } catch (error) {
        console.warn("[Proxy] Failed to setup HTTPS agent:", error);
      }
    }
  }

  // 使用node-fetch来确保SSL配置生效
  if (isNodeEnvironment()) {
    const fetch = await getNodeFetch();
    if (fetch && Agent) {
      // 为HTTPS请求设置SSL配置
      if (url.startsWith("https://") && !fetchOptions.agent) {
        const sslConfig = getSSLConfig(url);
        fetchOptions.agent = new Agent(sslConfig);
        console.log("[Proxy] Using node-fetch with SSL config for:", url);
      }

      try {
        console.log("[Proxy] Making request with node-fetch to:", url);
        const response = await fetch(url, fetchOptions);
        console.log("[Proxy] Request successful, status:", response.status);
        return response;
      } catch (error) {
        console.error("[Proxy] Node-fetch request failed:", error);
        // 使用SSL错误检测工具
        if (error instanceof Error && isSSLError(error)) {
          console.error("[Proxy] SSL Error detected. URL:", url);
          console.error(
            "[Proxy] SSL Error details:",
            getSSLErrorMessage(error, url),
          );
          console.error("[Proxy] Agent options:", fetchOptions.agent);
          checkSSLEnvironment();
        }
        throw error;
      }
    }
  }

  try {
    console.log("[Proxy] Making request with standard fetch to:", url);
    const response = await fetch(url, fetchOptions);
    console.log("[Proxy] Standard fetch successful, status:", response.status);
    return response;
  } catch (error) {
    console.error("[Proxy] Standard fetch failed:", error);
    throw error;
  }
}

// 导出默认的fetch函数，优先使用代理
export { proxyFetch as fetch };
export default proxyFetch;
