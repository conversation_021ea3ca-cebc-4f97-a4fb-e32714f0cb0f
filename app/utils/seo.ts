import { SEO_CONFIG } from "../config/seo";

// 生成页面SEO元数据
export function generatePageSEO({
  title,
  description,
  keywords = [],
  path = "/",
}: {
  title: string;
  description?: string;
  keywords?: string[];
  path?: string;
}) {
  const fullTitle = title.includes(SEO_CONFIG.SITE_NAME)
    ? title
    : `${title} | ${SEO_CONFIG.SITE_NAME}`;

  const finalDescription = description || SEO_CONFIG.SITE_DESCRIPTION;
  const url = `${SEO_CONFIG.SITE_URL}${path}`;

  return {
    title: fullTitle,
    description: finalDescription,
    keywords: [...SEO_CONFIG.KEYWORDS, ...keywords].join(", "),
    canonical: url,
    openGraph: {
      title: fullTitle,
      description: finalDescription,
      url,
      siteName: SEO_CONFIG.SITE_NAME,
      type: "website",
      locale: "zh_CN",
    },
    twitter: {
      card: "summary_large_image",
      title: fullTitle,
      description: finalDescription,
      creator: SEO_CONFIG.SOCIAL.twitter,
    },
  };
}

// 生成结构化数据
export function generateStructuredData() {
  return JSON.stringify(SEO_CONFIG.STRUCTURED_DATA);
}

// 生成robots.txt内容
export function generateRobotsTxt() {
  return `User-agent: *
Allow: /
Disallow: /api/
Disallow: /_next/

Sitemap: ${SEO_CONFIG.SITE_URL}/sitemap.xml`;
}
