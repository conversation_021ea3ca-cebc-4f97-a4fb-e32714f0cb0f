/**
 * 设备号管理工具
 */

import { safeLocalStorage } from "../utils";

const DEVICE_NO_KEY = "device_no";
const localStorage = safeLocalStorage();

type PlatformType = "android" | "ios" | "unknown";

/**
 * 生成Android设备号
 */
function generateAndroidDeviceNo(): string {
  const chars = "0123456789abcdef";
  let result = "android_";
  for (let i = 0; i < 16; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 生成iOS设备号
 */
function generateIOSDeviceNo(): string {
  const chars = "0123456789ABCDEF";
  const sections = [8, 4, 4, 4, 12];
  let result = "ios_";

  for (let i = 0; i < sections.length; i++) {
    if (i > 0) result += "-";
    for (let j = 0; j < sections[i]; j++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
  }
  return result;
}

/**
 * 检测平台类型
 */
function detectPlatform(): PlatformType {
  if (typeof navigator === "undefined") return "unknown";

  const userAgent = navigator.userAgent.toLowerCase();
  if (/android/.test(userAgent)) return "android";
  if (/iphone|ipad|ipod/.test(userAgent)) return "ios";
  return "unknown";
}

/**
 * 生成设备号
 */
function generateDeviceNo(): string {
  const platform = detectPlatform();
  switch (platform) {
    case "ios":
      return generateIOSDeviceNo();
    case "android":
    case "unknown":
    default:
      return generateAndroidDeviceNo();
  }
}

/**
 * 获取设备号
 */
export function getDeviceNo(): string {
  try {
    let deviceNo = localStorage.getItem(DEVICE_NO_KEY);

    if (!deviceNo) {
      deviceNo = generateDeviceNo();
      localStorage.setItem(DEVICE_NO_KEY, deviceNo);
    }

    return deviceNo;
  } catch (error) {
    return generateDeviceNo();
  }
}

/**
 * 重新生成设备号
 */
export function regenerateDeviceNo(): string {
  try {
    const newDeviceNo = generateDeviceNo();
    localStorage.setItem(DEVICE_NO_KEY, newDeviceNo);
    return newDeviceNo;
  } catch (error) {
    return generateDeviceNo();
  }
}

/**
 * 清除设备号
 */
export function clearDeviceNo(): void {
  try {
    localStorage.removeItem(DEVICE_NO_KEY);
  } catch (error) {
    // 忽略错误
  }
}

/**
 * 检查是否已有设备号
 */
export function hasDeviceNo(): boolean {
  try {
    return !!localStorage.getItem(DEVICE_NO_KEY);
  } catch (error) {
    return false;
  }
}

/**
 * 获取设备平台类型
 */
export function getDevicePlatform(): PlatformType {
  try {
    const deviceNo = localStorage.getItem(DEVICE_NO_KEY);
    if (!deviceNo) return "unknown";

    if (deviceNo.startsWith("android_")) return "android";
    if (deviceNo.startsWith("ios_")) return "ios";
    return "unknown";
  } catch (error) {
    return "unknown";
  }
}

/**
 * 验证设备号格式
 */
export function validateDeviceNo(deviceNo: string): boolean {
  if (!deviceNo || typeof deviceNo !== "string") return false;

  const androidPattern = /^android_[0-9a-f]{16}$/;
  const iosPattern =
    /^ios_[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/;

  return androidPattern.test(deviceNo) || iosPattern.test(deviceNo);
}
