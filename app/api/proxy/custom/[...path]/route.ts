import { NextRequest, NextResponse } from "next/server";
import { getServerSideConfig } from "@/app/config/server";
import { proxyFetch } from "@/app/utils/fetch";
import { isSSLError, getSSLErrorMessage } from "@/app/utils/ssl-config";

async function handle(
  req: NextRequest,
  { params }: { params: { path: string[] } },
) {
  console.log("[Custom Proxy Route] params ", params);

  if (req.method === "OPTIONS") {
    return NextResponse.json({ body: "OK" }, { status: 200 });
  }

  const serverConfig = getServerSideConfig();
  const baseUrl = process.env.CUSTOM_API_BASE_URL || "http://localhost:8080";

  // 构建目标URL
  const subpath = params.path.join("/");
  const searchParams = req.nextUrl.searchParams.toString();
  const targetUrl = `${baseUrl}/${subpath}${
    searchParams ? `?${searchParams}` : ""
  }`;

  console.log("[Custom Proxy] Target URL:", targetUrl);

  // 过滤请求头
  const skipHeaders = ["connection", "host", "origin", "referer", "cookie"];
  const headers = new Headers(
    Array.from(req.headers.entries()).filter((item) => {
      if (
        item[0].indexOf("x-") > -1 ||
        item[0].indexOf("sec-") > -1 ||
        skipHeaders.includes(item[0])
      ) {
        return false;
      }
      return true;
    }),
  );

  const controller = new AbortController();
  const timeoutId = setTimeout(() => {
    controller.abort();
  }, 30 * 1000); // 30秒超时

  try {
    const fetchOptions: RequestInit = {
      headers,
      method: req.method,
      body: req.body,
      signal: controller.signal,
      // @ts-ignore
      duplex: "half",
    };

    console.log("[Custom Proxy] Making request to:", targetUrl);
    console.log("[Custom Proxy] Request method:", req.method);
    console.log(
      "[Custom Proxy] Request headers:",
      Object.fromEntries(headers.entries()),
    );

    // 使用我们修改过的proxyFetch函数，它已经禁用了SSL验证
    const response = await proxyFetch(targetUrl, fetchOptions);

    console.log("[Custom Proxy] Response status:", response.status);
    console.log(
      "[Custom Proxy] Response headers:",
      Object.fromEntries(response.headers.entries()),
    );

    clearTimeout(timeoutId);

    // 创建新的响应，保持原始状态码和头部
    const responseHeaders = new Headers();
    response.headers.forEach((value, key) => {
      // 跳过一些可能导致问题的头部
      if (
        !["content-encoding", "content-length", "transfer-encoding"].includes(
          key.toLowerCase(),
        )
      ) {
        responseHeaders.set(key, value);
      }
    });

    // 添加CORS头部
    responseHeaders.set("Access-Control-Allow-Origin", "*");
    responseHeaders.set(
      "Access-Control-Allow-Methods",
      "GET, POST, PUT, DELETE, OPTIONS",
    );
    responseHeaders.set(
      "Access-Control-Allow-Headers",
      "Content-Type, Authorization",
    );

    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
    });
  } catch (error) {
    clearTimeout(timeoutId);
    console.error("[Custom Proxy] Error:", error);

    // 详细的错误信息用于调试
    let errorDetails = "Unknown error";
    let errorCode = "PROXY_ERROR";

    if (error instanceof Error) {
      errorDetails = error.message;

      // 使用SSL错误检测工具
      if (isSSLError(error)) {
        errorCode = "SSL_ERROR";
        errorDetails = getSSLErrorMessage(error, targetUrl);
      } else if (error.message.includes("ECONNREFUSED")) {
        errorCode = "CONNECTION_REFUSED";
        errorDetails = `连接被拒绝: ${error.message}. 请检查目标服务器是否正在运行。`;
      } else if (error.message.includes("ENOTFOUND")) {
        errorCode = "DNS_ERROR";
        errorDetails = `DNS解析失败: ${error.message}. 请检查域名是否正确。`;
      } else if (error.message.includes("timeout")) {
        errorCode = "TIMEOUT_ERROR";
        errorDetails = `请求超时: ${error.message}. 请检查网络连接或增加超时时间。`;
      }
    }

    console.error("[Custom Proxy] Error code:", errorCode);
    console.error("[Custom Proxy] Error details:", errorDetails);
    console.error("[Custom Proxy] Target URL:", targetUrl);
    console.error(
      "[Custom Proxy] Environment CUSTOM_API_BASE_URL:",
      process.env.CUSTOM_API_BASE_URL,
    );

    return NextResponse.json(
      {
        error: "Custom proxy request failed",
        code: errorCode,
        details: errorDetails,
        targetUrl: targetUrl,
        timestamp: new Date().toISOString(),
      },
      { status: 500 },
    );
  }
}

export const GET = handle;
export const POST = handle;
export const PUT = handle;
export const DELETE = handle;
export const PATCH = handle;
export const OPTIONS = handle;
