/**
 * Key API - 设备密钥相关功能
 */

import { apiClient } from "../../utils/api-client";
import { getDeviceNo } from "../../utils/device";

// 加载SM加密库
let sm2: any, sm4: any;

try {
  const smCrypto = require("sm-crypto");
  sm2 = smCrypto.sm2;
  sm4 = smCrypto.sm4;
} catch (error) {
  console.error("Failed to load sm-crypto library:", error);
}

// ==================== 常量定义 ====================

/** SM2公钥 */
export const SM2_PUBLIC_KEY =
  "0457D439E67908DC998F9B25D51C4D499A031CEACA885FE8E1FF2EA620B9BE6E64F83EAF5F04AA1032DB6C27268586BADDEC3FF6858DE1D6CA750BC8AB54CA5889";

/**
 * 生成32位随机十六进制字符串
 */
function generateMasterKey(): string {
  const chars = "0123456789ABCDEF";
  let result = "";
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/** 主密钥 - 32位随机生成的十六进制字符串 */
const MASTER_KEY = generateMasterKey();

// ==================== 类型定义 ====================

/**
 * 申请私钥请求参数接口
 */
export interface ApplyPrivateKeyRequest {
  deviceNo: string;
  masterKey: string;
  sign: string;
}
/**
 * 申请私钥响应接口
 */
export interface ApplyPrivateKeyResponse {
  key: string;
}

// ==================== 核心加密处理函数 ====================

/**
 * 处理主密钥的加密流程
 * 1. Base64编码
 * 2. SM2加密
 * 3. 添加前缀"04"
 */
function processMasterKey(): string {
  try {
    const base64MasterKey = btoa(MASTER_KEY);

    // 使用与 sm-crypto.ts 一致的加密方式
    const encryptedData = sm2.doEncrypt(base64MasterKey, SM2_PUBLIC_KEY, 1);

    return "04" + encryptedData;
  } catch (error) {
    throw new Error(
      `主密钥处理失败: ${
        error instanceof Error ? error.message : String(error)
      }`,
    );
  }
}

/**
 * 生成设备号的SM4加密签名
 * @param deviceNo 设备号
 * @param encryptionKey 加密密钥
 */
function generateDeviceSign(deviceNo: string, encryptionKey: string): string {
  try {
    return sm4.encrypt(deviceNo, encryptionKey);
  } catch (error) {
    throw new Error(
      `设备签名生成失败: ${
        error instanceof Error ? error.message : String(error)
      }`,
    );
  }
}

// ==================== 公共API接口 ====================

/**
 * 组装申请私钥的请求参数
 */
export function buildApplyPrivateKeyRequest(
  deviceNo?: string,
): ApplyPrivateKeyRequest {
  try {
    const finalDeviceNo = deviceNo || getDeviceNo();
    const processedMasterKey = processMasterKey();
    const sign = generateDeviceSign(finalDeviceNo, MASTER_KEY);

    return {
      deviceNo: finalDeviceNo,
      masterKey: processedMasterKey,
      sign,
    };
  } catch (error) {
    throw new Error(
      `请求参数组装失败: ${
        error instanceof Error ? error.message : String(error)
      }`,
    );
  }
}

/**
 * 申请私钥API调用
 */
export async function applyPrivateKey(
  deviceNo?: string,
): Promise<ApplyPrivateKeyResponse> {
  try {
    const request = buildApplyPrivateKeyRequest(deviceNo);
    return await apiClient.post("/device/applyPrivateKey", request, {
      withEncryption: false,
    });
  } catch (error) {
    throw new Error(
      `私钥申请失败: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
}

// ==================== 登录页面私钥管理 ====================

/** 私钥存储键名 */
const PRIVATE_KEY_STORAGE_KEY = "device_private_key";

/**
 * 存储设备私钥到本地
 */
export function storePrivateKey(privateKey: string): void {
  try {
    localStorage.setItem(PRIVATE_KEY_STORAGE_KEY, privateKey);
  } catch (error) {
    console.error("存储私钥失败:", error);
  }
}

/**
 * 获取本地存储的设备私钥
 */
export function getStoredPrivateKey(): string | null {
  try {
    return localStorage.getItem(PRIVATE_KEY_STORAGE_KEY);
  } catch (error) {
    console.error("获取私钥失败:", error);
    return null;
  }
}

/**
 * 清除本地存储的设备私钥
 */
export function clearStoredPrivateKey(): void {
  try {
    localStorage.removeItem(PRIVATE_KEY_STORAGE_KEY);
  } catch (error) {
    console.error("清除私钥失败:", error);
  }
}

/**
 * 登录页面初始化 - 申请并存储设备私钥
 * 进入登录页面时获取一次私钥并更新本地存储
 */
export async function initLoginPageSecurity(): Promise<{
  deviceNo: string;
  privateKey: string;
  success: boolean;
}> {
  try {
    // 1. 获取当前设备号
    const deviceNo = getDeviceNo();

    // 2. 申请设备私钥
    const response = await applyPrivateKey(deviceNo);

    // 3. 提取私钥
    const privateKey = response.key;

    if (!privateKey) {
      throw new Error("API响应中未找到私钥");
    }

    // 4. SM4解密key
    const decryptedPrivateKey = sm4.decrypt(privateKey, MASTER_KEY);

    // 5. 存储设备号和私钥到本地
    storePrivateKey(decryptedPrivateKey);

    return {
      deviceNo,
      privateKey: decryptedPrivateKey,
      success: true,
    };
  } catch (error) {
    console.error("登录页面安全初始化失败:", error);
    return {
      deviceNo: getDeviceNo(),
      privateKey: "",
      success: false,
    };
  }
}
