/**
 * API模块统一导出
 * 提供所有API模块的统一入口
 */

// 导出API客户端
export * from "../../utils/api-client";

// 导出错误处理工具
export * from "../../utils/error-handler";

// 导出认证API
export * from "./auth-api";

// 导出Stripe API
export * from "./stripe-api";

// 导出基础API
export * from "./key-api";

// 导出AI工具API
export * from "./ai-tool-api";

// 重新导出常用的API类，方便使用
export { AuthApi } from "./auth-api";
export { StripeApi } from "./stripe-api";

// 创建API模块的命名空间导出
import { AuthApi } from "./auth-api";
import { StripeApi } from "./stripe-api";
import { apiClient } from "../../utils/api-client";
import { ErrorHandler } from "../../utils/error-handler";

// 统一的API对象
export const API = {
  // 基础客户端实例
  client: apiClient,

  // 错误处理器
  errorHandler: ErrorHandler,

  // 认证模块
  auth: AuthApi,

  // 支付模块
  stripe: StripeApi,
} as const;

// 默认导出
export default API;
