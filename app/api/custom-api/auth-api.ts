/**
 * 认证模块API接口
 * 使用统一的API客户端进行请求处理
 */

import { clearAuthToken, setAuthToken } from "@/app/utils/auth-token";
import { apiClient } from "../../utils/api-client";

// 登录请求接口
export interface LoginRequest {
  account: string;
  password: string;
  type: number;
}

// 注册请求接口
export interface RegisterRequest {
  account: string;
  password: string;
  email: string;
  sourceType: number;
}

// 用户信息接口
export interface UserProfile {
  account: string;
  orgCode: string;
  loginTime: string;
}

export const setUserInfo = (user: UserProfile): void => {
  if (typeof window === "undefined") return;
  localStorage.setItem("user_info", JSON.stringify(user));
};

export const getUserInfo = (): UserProfile | null => {
  if (typeof window === "undefined") return null;
  const userInfo = localStorage.getItem("user_info");
  return userInfo ? JSON.parse(userInfo) : null;
};

export const clearUserInfo = (): void => {
  if (typeof window === "undefined") return;
  localStorage.removeItem("user_info");
};

// 认证API类
export class AuthApi {
  // 登录
  static async login(data: LoginRequest): Promise<any> {
    const response = await apiClient.post<any>("/app/user/login", data, {
      withAuth: false,
    });

    const { token, loginUser } = response;

    // 自动保存认证信息
    if (token) setAuthToken(token);
    if (loginUser) setUserInfo(loginUser);

    return response;
  }

  // 注册
  static async register(data: RegisterRequest): Promise<any> {
    const response = await apiClient.post<any>("/app/user/register", data, {
      withAuth: false,
    });

    return response;
  }

  // 登出
  static async logout(): Promise<void> {
    try {
      await apiClient.post("/app/user/loginout", {});
    } catch (error) {
      console.error("Logout API error:", error);
    } finally {
      // 无论API调用是否成功，都清除本地认证信息
      clearAuthToken();
      clearUserInfo();
    }
  }
}

// 导出便捷方法（保持向后兼容）
export const loginApi = AuthApi.login;
export const registerApi = AuthApi.register;
export const logoutApi = AuthApi.logout;
