import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const { method, headers, body } = await req.json();
    const targetUrl = req.nextUrl.searchParams.get("target");
    const proxyUrl = req.nextUrl.searchParams.get("proxy");

    if (!targetUrl) {
      return NextResponse.json(
        { error: "Target URL is required" },
        { status: 400 },
      );
    }

    if (!proxyUrl) {
      return NextResponse.json(
        { error: "Proxy URL is required" },
        { status: 400 },
      );
    }

    console.log(`[Proxy Request] ${method} ${targetUrl} via ${proxyUrl}`);

    // 在Node.js环境中使用代理
    let fetchOptions: RequestInit = {
      method: method || "GET",
      headers: headers || {},
    };

    if (body && method !== "GET" && method !== "HEAD") {
      fetchOptions.body = body;
    }

    // 尝试使用代理agent
    try {
      if (typeof require !== "undefined") {
        const { HttpsProxyAgent } = require("https-proxy-agent");
        const { HttpProxyAgent } = require("http-proxy-agent");
        const { SocksProxyAgent } = require("socks-proxy-agent");

        let agent;
        const targetUrlObj = new URL(targetUrl);

        if (proxyUrl.startsWith("socks")) {
          agent = new SocksProxyAgent(proxyUrl);
        } else if (targetUrlObj.protocol === "https:") {
          agent = new HttpsProxyAgent(proxyUrl);
        } else {
          agent = new HttpProxyAgent(proxyUrl);
        }

        // @ts-ignore
        fetchOptions.agent = agent;
        console.log(
          `[Proxy Request] Using ${agent.constructor.name} for ${targetUrl}`,
        );
      }
    } catch (error) {
      console.warn("[Proxy Request] Failed to create proxy agent:", error);
    }

    const response = await fetch(targetUrl, fetchOptions);

    // 创建新的响应，保持原始状态码和头部
    const responseHeaders = new Headers();
    response.headers.forEach((value, key) => {
      // 跳过一些可能导致问题的头部
      if (
        !["content-encoding", "content-length", "transfer-encoding"].includes(
          key.toLowerCase(),
        )
      ) {
        responseHeaders.set(key, value);
      }
    });

    // 添加CORS头部
    responseHeaders.set("Access-Control-Allow-Origin", "*");
    responseHeaders.set(
      "Access-Control-Allow-Methods",
      "GET, POST, PUT, DELETE, OPTIONS",
    );
    responseHeaders.set(
      "Access-Control-Allow-Headers",
      "Content-Type, Authorization",
    );

    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
    });
  } catch (error) {
    console.error("[Proxy Request] Error:", error);
    return NextResponse.json(
      {
        error: "Proxy request failed",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

export async function OPTIONS(req: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
