import { Analytics } from "@vercel/analytics/react";
import { Home } from "./components/home";
import { getServerSideConfig } from "./config/server";
import type { Metadata } from "next";

const serverConfig = getServerSideConfig();

export const metadata: Metadata = {
  title: "Creative AI - 免费AI聊天助手 | 智能对话平台",
  description:
    "Creative AI提供免费的AI聊天服务，支持DeepSeek、GPT等多种AI模型。智能对话、代码编程、文本生成，体验最先进的人工智能技术。立即开始您的AI之旅！",
  keywords: [
    "免费AI聊天",
    "AI助手",
    "人工智能对话",
    "DeepSeek",
    "ChatGPT",
    "智能问答",
    "代码助手",
    "AI编程",
  ],
  openGraph: {
    title: "Creative AI - 免费AI聊天助手",
    description: "体验最先进的AI聊天技术，支持多种AI模型，完全免费使用。",
    type: "website",
    url: "https://www.51creativeai.com",
  },
};

export default async function App() {
  return (
    <>
      <main role="main" itemScope itemType="https://schema.org/WebApplication">
        <meta itemProp="name" content="Creative AI" />
        <meta
          itemProp="description"
          content="智能AI聊天助手平台，支持多种AI模型"
        />
        <meta itemProp="url" content="https://www.51creativeai.com" />
        <Home />
      </main>
      {serverConfig?.isVercel && (
        <>
          <Analytics />
        </>
      )}
    </>
  );
}
